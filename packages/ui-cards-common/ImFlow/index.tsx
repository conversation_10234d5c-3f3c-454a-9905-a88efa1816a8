import {Image, Text, View} from '@tarojs/components';
import {memo, useCallback, useMemo, type FC, useState, useEffect, useRef} from 'react';
import cx from 'classnames';
import {WImage} from '@baidu/wz-taro-tools-core';
import {eventCenter} from '@tarojs/taro';
import LongPressMenu from '../../pages-im/src/components/LongPressMenu';
import {useLongPressMenu} from '../../pages-im/src/hooks/useLongPressMenu';
import {useHeightObserver} from '../../pages-im/src/hooks/common/useHeightObserver';
import {ubcCommonViewSend} from '../../pages-im/src/utils/generalFunction/ubc';
import {useMockTypewriterControl} from './hooks/useMockTypewriterControl';

import {useUbc} from './hooks/index';

import MarkDown from './components/MarkDown';
import QuickReply from './components/QuickReply';
import MarkDownBtn from './components/MarkDownBtn';
import ContactInfo from './components/ContactInfo';
import SourceLinks from './components/SourceLinks';
import Endorse‌Card from './components/Endorse‌Card';
import MarkdownPlan from './components/MarkdownPlan';
import MarkDownList from './components/MarkDownList';
import MarkDownImages from './components/MarkDownImages';
import MarkDownVideos from './components/MarkDownVideos';
import ThinkingChain from './components/ThinkingChainV2';
import MarkdownTabList from './components/MarkdownTabList';
import MarkDownCard from './components/MarkDownCard/index';
import SearchReferencesCard from './components/SearchReferences';
import RhetoricalQuestions from './components/RhetoricalQuestions';
import RenderComponentControl from './components/RenderComponentControl';

import styles from './index.module.less';

import type {ImFlowProps, FeedbackPopupDataProps, ImFlowData} from './index.d';

// header组件映射
const headerMapComponent = {
    expertReview: Endorse‌Card
};

const ImFlow: FC<ImFlowProps> = (msgData: ImFlowProps) => {
    const {
        data,
        msgId,
        isLocalInterrupted,
        localExt,
        page = 'im',
        isLatest,
        lastMsgId,
        sessionId,
        experienceConfig,
        customStyles
    } = msgData || {};
    const {action, searchReferences} = data || {};
    const {
        list,
        quickReply = [],
        rhetoricalQuestions = [],
        isInterrupted,
        sourceInfo = null
    } = data?.content?.data?.content || {};

    const ubc = data?.content?.ubc;
    const interrupted = isLocalInterrupted || isInterrupted;
    const {dataSource} = localExt || {};
    const {renderType} = data?.content?.data?.cardStyle || {};
    // 服务端新增打点拓展字段 和cardStyle平级
    const {ext = {}} = data?.content?.data || {};
    const {menuOpenStatus, menuPosition, openMenu, closeMenu} = useLongPressMenu();
    const [h5Position, setH5Position] = useState({x: 0, y: 0});

    const [tabListSummaryEnd, setTabListSummaryEnd] = useState(true);
    // 记录当前正在打印的 tabList 数量
    const [printingTabListCount, setPrintingTabListCount] = useState(0);
    const [tabIndex, setTabIndex] = useState(0);

    const hasReportQcUbc = useRef(false); // 标记是否已经上报过 QC UBC

    const imFlowId = useMemo(
        () => `im-flow-${msgData.msgId || Math.random().toString(36).substring(2)}`,
        [msgData.msgId]
    );

    // 是否需要滚动到最新消息标识由数据层处理；
    const needScrollToBottom = useMemo(() => {
        return msgData?.localExt?.needScrollToBottom;
    }, [msgData?.localExt?.needScrollToBottom]);

    const isRenderType2 = useMemo(() => renderType === 2, [renderType]);

    const memoIsLastMsg = useMemo(() => {
        return lastMsgId === msgId && action === 'end';
    }, [action, lastMsgId, msgId]);

    const tabListSummaryCondition = useMemo(() => {
        // 如果当前有正在打印的 tabList，则不渲染
        if (printingTabListCount > 0) {
            return false;
        }
        return true;
    }, [printingTabListCount]);

    const quickReplyRenderCondition = useMemo(() => {
        // 如果当前有正在打印的 tabList，则不渲染
        if (isRenderType2) {
            return tabListSummaryEnd;
        }
        return true;
    }, [isRenderType2, tabListSummaryEnd]);

    // 长按消息层
    const handleLongPress = useCallback(
        e => {
            e?.preventDefault();

            if (!msgData || !experienceConfig) {
                return;
            }

            let position = e?.detail;
            if (process.env.TARO_ENV === 'h5') {
                position = h5Position;
            }
            openMenu(position, true);
        },
        [experienceConfig, h5Position, msgData, openMenu]
    );

    const handleTouchStart = useCallback(e => {
        const position = {
            x: e?.touches[0].clientX,
            y: e?.touches[0].clientY
        };
        setH5Position(position);
    }, []);

    // TODO: 跟外层方法合并@gongjianmei
    const convertData = useMemo(() => {
        // todo暂取最后一段content(待确认)
        const originList = data?.content?.data?.content?.list || [];
        const targetContent = originList[originList.length - 1] || {};
        const {content, tabList} = targetContent;
        if (content && !tabList) {
            return content;
        }
        let tabContent = '';
        if (tabList) {
            const {header, body} = tabList;
            if (header?.length) {
                tabContent += header.map(item => `${item.title}：${item.content}\n`).join('');
            }
            if (body?.length) {
                const {title = '', list = []} = body[tabIndex];
                tabContent += `\n${title}\n`;
                tabContent += list.map(item => `\n${item.title}\n${item.content}`).join('');
            }
        }
        if (content) {
            tabContent += `\n${content}`;
        }
        return tabContent || '';
    }, [data?.content?.data?.content?.list, tabIndex]);

    const changeTab = useCallback(async (index: number) => {
        setTabIndex(index);
        eventCenter.trigger('stopTTS');
    }, []);

    useEffect(() => {
        eventCenter.on('changeMsgTab', changeTab);
        return () => {
            eventCenter.off('changeMsgTab', changeTab);
        };
    }, [changeTab]);

    // 初始化高度变化监听hook
    useHeightObserver(
        imFlowId,
        needScrollToBottom,
        'dom_height_observer',
        page === 'im' ? 'triageStreamScrollControl' : 'docImScrollControl'
    );

    const {commonUbcInteraction} = useUbc({
        ubc,
        dataSource
    });

    // imflow 统一ubc打点，携带意图类型, 卡片时候触发
    useEffect(() => {
        if (action === 'end') {
            commonUbcInteraction({
                type: 'view'
            });
        }
    }, [action, commonUbcInteraction]);

    // imflow 针对消费医疗特殊场景埋点
    useEffect(() => {
        if (ext?.contentTrack && !hasReportQcUbc.current) {
            ubcCommonViewSend({
                value: 'qcCaseMsg',
                ext: {
                    ...ext,
                    msgId
                }
            });
            hasReportQcUbc.current = true;
        }
    }, [ext, msgId]);

    // 渲染 Markdown 卡片头部
    const renderMarkDownCardHeader = useCallback(
        ({title, icon, sectionHeader}) => {
            if (isRenderType2) {
                return (
                    <View className={styles.markdownCardHeader}>
                        <View className={styles.markdownCardHeaderIcon}>
                            <Image
                                className={styles.markdownCardHeaderIconImg}
                                src={icon}
                                mode='aspectFit'
                            />
                        </View>
                        <Text className={styles.markdownCardHeaderTitle}>{title}</Text>
                    </View>
                );
            }
            const headerType = sectionHeader?.type;
            if (headerType) {
                const HeaderComponent = headerMapComponent[headerType];
                return <HeaderComponent {...sectionHeader} />;
            }

            return null;
        },
        [isRenderType2]
    );

    const stableSearchReferencesTitle = useMemo(
        () => searchReferences?.title,
        [searchReferences?.title]
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const stableExt = useMemo(() => ext, [JSON.stringify(ext)]);

    // 使用模拟打字机 hook
    const {shouldShowMedia, handleTypewriterSuccess} = useMockTypewriterControl();

    // 渲染卡片组件 - 简化后的逻辑
    const renderMsgComponents = useCallback(
        list => {
            return list
                ?.map((_item, _index) => {
                    const msgEnd = action === 'end';
                    const {
                        plan,
                        content,
                        markdownBtn,
                        docContactInfo,
                        media,
                        isFinish = false,
                        sectionShowMore,
                        contentList,
                        tabList,
                        sectionHeader,
                        planProgressDesc,
                        planIcon,
                        isMockTypeWriter = false
                    } = _item || {};
                    const {images, videos, sourceLinks} = media || {};
                    const {text, icon} = sourceInfo || {};

                    const sectionId = _item?.sectionId;

                    // // 使用 hook 中的方法检查是否应该渲染
                    // if (!shouldRenderItem(list, _index)) {
                    //     return null;
                    // }

                    // 使用 hook 中的方法检查媒体组件是否应该显示
                    const isRenderMockTypewriter =
                        dataSource === 'conversation'
                            ? shouldShowMedia(isMockTypeWriter, sectionId)
                            : true;

                    // 长科普需要前端mock打字机的情况下，stepWordNum 设置成5
                    const stepWordNum = isMockTypeWriter ? 2 : 1;

                    const isQuickSkuReply = markdownBtn ? true : false;
                    const isSectionLast = _index === list.length - 1;

                    // 如果打断消息，且不是快速回复，则渐变动画不消失
                    const forcedShowLoading = interrupted && !isQuickSkuReply && isSectionLast;
                    const isMockTypeWriterEnd = isMockTypeWriter ? isRenderMockTypewriter : msgEnd;

                    return (
                        <MarkDownCard
                            msgData={msgData}
                            key={msgId + _item?.sectionId}
                            scrollKey={msgId + _item?.sectionId}
                            isSectionLast={isSectionLast && isMockTypeWriterEnd}
                            isLatest={isLatest}
                            page={page}
                            className={
                                searchReferences?.title && _index === 0
                                    ? styles.markDownCardNoBorder
                                    : ''
                            }
                            isInterrupted={interrupted}
                            isQuickSkuReply={isQuickSkuReply}
                            header={renderMarkDownCardHeader({
                                title: planProgressDesc,
                                icon: planIcon,
                                sectionHeader
                            })}
                            sectionShowMore={sectionShowMore}
                            commonUbcInteraction={commonUbcInteraction}
                        >
                            <View className='imFlowContent'>
                                {/* 信息来源提示 */}
                                {text && (
                                    <View
                                        className={cx(
                                            styles.sourchInfo,
                                            'wz-flex wz-fs-42 wz-col-top'
                                        )}
                                    >
                                        {icon && (
                                            <WImage
                                                className={cx(styles.icon, 'wz-mr-18 flex-1')}
                                                src={icon}
                                            />
                                        )}
                                        <View className='wz-taro-ellipsis'>{text}</View>
                                    </View>
                                )}
                                {/* 渲染步骤组件 */}
                                <RenderComponentControl data={plan}>
                                    {isRenderType2 ? (
                                        <ThinkingChain
                                            plan={plan}
                                            forceNoShow={
                                                content || tabList || quickReply?.length > 0
                                            }
                                        />
                                    ) : (
                                        <MarkdownPlan plan={plan} msgEnd={msgEnd || content} />
                                    )}
                                </RenderComponentControl>

                                {/* 渲染markdown组件 */}
                                <RenderComponentControl data={!isRenderType2 && content}>
                                    <MarkDown
                                        isTypewriter={dataSource === 'conversation'}
                                        loadingType=':gradient:'
                                        className={plan?.length ? styles.markdownMargin : ''}
                                        content={content}
                                        isMockTypwer={isMockTypeWriter}
                                        forcedShowLoading={forcedShowLoading}
                                        stepWordNum={stepWordNum}
                                        speed={isMockTypeWriter ? 10 : 20}
                                        // isFinish 如果是mock打字机，则判断是否是最后一张卡，否则通过 msgEnd || 非最后一张卡来判断
                                        isFinish={
                                            isMockTypeWriter ? !isLatest : msgEnd || !isSectionLast
                                        }
                                        msgEnd={isMockTypeWriter ? !isLatest : msgEnd}
                                        typewriterSuccessCallback={() => {
                                            if (isMockTypeWriter && isLatest) {
                                                handleTypewriterSuccess(sectionId);
                                            }
                                        }}
                                        customStyles={customStyles?.markDown}
                                    />
                                </RenderComponentControl>
                                {/* 渲染markdownList组件 */}
                                <RenderComponentControl data={contentList}>
                                    <MarkDownList
                                        contentList={contentList}
                                        globalProps={{
                                            msgEnd,
                                            interrupted,
                                            dataSource,
                                            contentList,
                                            msgId,
                                            ext,
                                            forcedShowLoading,
                                            commonUbcInteraction,
                                            customStyles
                                        }}
                                    />
                                </RenderComponentControl>
                                {/* renderType为2时，需要渲染tab组件 */}
                                <RenderComponentControl data={isRenderType2 && tabList}>
                                    <MarkdownTabList
                                        data={tabList}
                                        isTypewriter={dataSource === 'conversation'}
                                        onStart={() => setPrintingTabListCount(prev => prev + 1)}
                                        onComplete={() => setPrintingTabListCount(prev => prev - 1)}
                                    />
                                </RenderComponentControl>
                                <RenderComponentControl
                                    data={isRenderType2 && tabListSummaryCondition && content}
                                >
                                    <MarkDown
                                        isTypewriter
                                        loadingType=':gradient:'
                                        markdownClassName={cx(styles.tabListSummary)}
                                        {..._item}
                                        content={content}
                                        isFinish={msgEnd}
                                        msgEnd={msgEnd}
                                        customStyles={customStyles?.markDown}
                                        typewriterStartCallback={() => setTabListSummaryEnd(false)}
                                        typewriterSuccessCallback={() => setTabListSummaryEnd(true)}
                                    />
                                </RenderComponentControl>
                                {/* 渲染markdown按钮组件 */}
                                <RenderComponentControl data={markdownBtn}>
                                    <MarkDownBtn
                                        markdownBtn={markdownBtn}
                                        msgId={msgId}
                                        page={page}
                                        ext={ext}
                                    />
                                </RenderComponentControl>

                                {/* 渲染ContactInfo组件 */}
                                <RenderComponentControl data={docContactInfo}>
                                    <ContactInfo
                                        docContactInfo={docContactInfo}
                                        msgId={msgId}
                                        ext={ext}
                                    />
                                </RenderComponentControl>

                                {/* 渲染markdown图片组件 */}
                                <RenderComponentControl
                                    isShow={isRenderMockTypewriter}
                                    data={images}
                                >
                                    <MarkDownImages
                                        images={images}
                                        msgId={msgId}
                                        ext={ext}
                                        isFinish={isFinish || msgEnd}
                                    />
                                </RenderComponentControl>

                                {/* 渲染markdown视频组件 */}
                                <RenderComponentControl
                                    isShow={isRenderMockTypewriter}
                                    data={videos}
                                >
                                    <MarkDownVideos
                                        videos={videos}
                                        msgId={msgId}
                                        ext={ext}
                                        isFinish={isFinish || msgEnd}
                                    />
                                </RenderComponentControl>

                                {/* 渲染sourceLinks组件 */}
                                <RenderComponentControl
                                    isShow={isRenderMockTypewriter}
                                    data={sourceLinks}
                                >
                                    <SourceLinks
                                        list={sourceLinks}
                                        commonUbcInteraction={commonUbcInteraction}
                                        msgEnd={action === 'end'}
                                    />
                                </RenderComponentControl>
                            </View>
                        </MarkDownCard>
                    );
                })
                .filter(Boolean); // 过滤掉 null 值
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [
            action,
            msgData,
            stableSearchReferencesTitle,
            interrupted,
            renderMarkDownCardHeader,
            isRenderType2,
            quickReply?.length,
            dataSource,
            msgId,
            stableExt,
            tabListSummaryCondition,
            shouldShowMedia,
            page,
            sourceInfo
        ]
    );

    return (
        <>
            <View id={imFlowId} className={`${styles.imFlow}`}>
                <View
                    onLongPress={e => handleLongPress(e)}
                    onLongTap={e => handleLongPress(e)}
                    onTouchStart={e => handleTouchStart(e)}
                >
                    {/* 渲染思考链一组件 */}
                    <RenderComponentControl data={searchReferences?.title}>
                        <MarkDownCard
                            msgData={msgData}
                            key='searchReferencesCard'
                            className={styles.searchReferencesMarkDown}
                        >
                            <SearchReferencesCard
                                searchReferences={searchReferences}
                                msgEnd={action === 'end'}
                            />
                        </MarkDownCard>
                    </RenderComponentControl>

                    {/* 渲染卡片组件 */}
                    {renderMsgComponents(list)}
                </View>

                {/* 渲染反问组件 */}
                <RenderComponentControl data={rhetoricalQuestions?.length}>
                    <RhetoricalQuestions
                        page={page}
                        rhetoricalQuestions={rhetoricalQuestions}
                        msgId={msgId}
                        ext={ext}
                        readonly={!memoIsLastMsg}
                        customStyles={customStyles?.rhetoricalQuestions}
                    />
                </RenderComponentControl>

                {/* 渲染追问组件 */}
                <RenderComponentControl
                    data={quickReplyRenderCondition && memoIsLastMsg && quickReply?.length}
                >
                    <QuickReply
                        quickReply={quickReply}
                        msgId={msgId}
                        ext={ext}
                        page={page}
                        customStyles={customStyles?.quickReply}
                    />
                </RenderComponentControl>
            </View>

            {/* 渲染长按菜单 */}
            <LongPressMenu
                open={menuOpenStatus}
                originText={convertData}
                position={menuPosition}
                msgId={msgId}
                feature={msgData?.data?.feature}
                sessionId={sessionId}
                experienceConfig={experienceConfig}
                onClose={closeMenu}
            />
        </>
    );
};

ImFlow.displayName = 'ImFlow';
export {type ImFlowProps, type FeedbackPopupDataProps, type ImFlowData};
export default memo(ImFlow);
