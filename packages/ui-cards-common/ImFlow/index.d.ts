import type {ExperienceConfig} from '../../pages-im/src/models/services/triageStream/index.d';
import {CSSProperties} from 'react';

import type {
    MsgInstanceData,
    InteractionType,
    InteractionInfo
} from '../../pages-im/src/typings/msg.type';
import {type ComponentProps} from './components/MarkDownList/index';
import type {SourceLinksProps} from './components/SourceLinks/index.d';

export interface FeedbackPopupDataProps {
    type: string;
    content: FeedbackPopupData;
}

interface Input {
    placeholder: string;
    value: string;
}

interface Item {
    value: string;
}

interface Content {
    items: Item[];
    subTitle: string;
}

interface FeedbackPopupData {
    input: Input;
    title: string;
    content: Content[];
    submitBtn: {
        value: string;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export interface ActionInfo {
    value: string;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface UbcExt {
    [key: string]: unknown;
}
export interface Ubc {
    value: string;
    productionInfo?: UbcExt;
}

type CommonUbcInteraction = (params: {type: 'clk' | 'view'; value: string; ext?: UbcExt}) => void;

export interface Ext {
    [key: string]: string | number;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    actionInfo?: ActionInfo;
    content: T;
    ext?: Ext;
}

export interface SearchReferences {
    title: string;
    content: string;
    isFinish: boolean;
}

export interface MarkDdownTableCustomStyles {
    thBgColors?: string[];
    tableStyle?: CSSProperties;
    tableContentStyle?: CSSProperties;
    cellStyle?: CSSProperties;
    firstCellStyle?: CSSProperties;
    thCellStyle?: CSSProperties;
}

export type Action = 'init' | 'append' | 'end';

export type DataSourceType = 'conversation' | 'history' | 'mock';

export type InsertType = 'unshift' | 'push';

export interface ImFlowProps {
    data: MsgInstanceData<ImFlowData>;
    msgId: string;
    lastMsgId: string;
    sessionId: string;
    isLocalInterrupted?: boolean;
    isLatest?: boolean;
    localExt: {
        dataSource: DataSourceType;
        insertType?: InsertType;
        needScrollToBottom?: boolean;
    };
    // 基于页面区分
    page?: 'docIm' | 'im';
    experienceConfig?: ExperienceConfig;
    // 样式控制
    customStyles?: {
        rhetoricalQuestions?: {
            paidActiveBgColor: string;
        };
        quickReply?: {
            paidActiveBgColor: string;
        };
        markDown?: {
            table?: MarkDdownTableCustomStyles;
        };
    };
}

interface QuickReply {
    content: string;
}

interface RhetoricalQuestion {
    question: string;
    options: {content: string}[];
}
interface TextList {
    sectionId: string;
    content: string;
    isFinish: boolean;
}

interface MediaInfo {
    images?: {
        icon?: string;
        origin?: string;
        small?: string;
        hasMask?: boolean;
        maskInfo?: {
            text?: string;
        };
    }[];
    videos?: {
        origin?: string;
        thumb?: string;
        width?: number;
        height?: number;
    }[];
    sourceLinks?: SourceLinksProps;
}
interface ExpertReviewPopupListItem {
    docId: string;
    portrait: string;
    name: string;
    department: string;
    title: string;
    hospital: string;
    hospitalLevel: string;
    likeCount: number;
    hospitalFudanRank: string;
    departmentFudanRank: string;
    goodAt: string;
    homeUrlParams: {
        url: string;
    };
}

export interface ExpertReview {
    headerInfo: {
        avatarList: string[];
        reviewNote: string;
        actionInfo: {
            interaction: InteractionType;
            interactionInfo: {
                popupInfo: {
                    content: ExpertReviewPopupListItem[];
                };
            };
        };
    };
}

export interface ContentList {
    ids: string[];
    data: Record<
        string,
        {
            contentAdd?: string;
            contentReplace?: ComponentProps['content'];
            isFinish?: boolean;
            component: CardComponentType;
            [key: string]: unknown;
        }
    >;
}

export interface SectionShowMore {
    content: string;
}

export interface ImFlowData {
    list: {
        sectionId: string;
        content: string;
        isFinish: boolean;
        sectionHeader?: {
            type: string;
            content: ExpertReview;
        };
        isMockTypeWriter?: boolean;
        planIcon?: string;
        planProgressDesc?: string;
        plan?: {
            id: string;
            name: string;
            status: 'doing' | 'completed';
            description: string;
        }[];
        markdownBtn?: {
            description: string;
            actionInfo: ActionInfo;
        };
        media?: MediaInfo;
        tabList?: {
            header: {
                title: string;
                content: string; // md 格式
            }[];
            body: {
                title: string;
                list: {
                    title: string;
                    content: string; // md 格式
                }[];
                media: MediaInfo;
            }[];
            tabTitle: string;
            content?: string; // 保持一致 markdown 格式；
        };
        contentList?: ContentList;
        type: 'markdown' | 'markdownList';
        sectionShowMore?: SectionShowMore;
    }[];
    quickReply?: QuickReply[];
    rhetoricalQuestions?: RhetoricalQuestion[]; // 反问模块
    isInterrupted?: boolean;
    sourceInfo?: {
        text: string;
        icon?: string;
    };
}

export interface CardStyle {
    [key: string]: string | number;
}

export enum CardComponent {
    'markdown' = 'MarkDown',
    'markdownBtn' = 'MarkDownBtn',
    'medicalCard' = 'MedicalCard',
    'searchReferences' = 'SearchReferencesCard'
}

export type CardComponentType = `${keyof typeof CardComponent}`;

export type ComponentUbcPosProps = {
    componentIdsList: string[];
    pos: number;
};
