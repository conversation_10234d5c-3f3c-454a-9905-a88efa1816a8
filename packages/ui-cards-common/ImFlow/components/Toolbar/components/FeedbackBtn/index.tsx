import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {WiseLike, WiseDislike, WiseLikeSolid, WiseDislikeSolid} from '@baidu/wz-taro-tools-icons';

import {useGetCardEventCallback} from '../../../../../../pages-im/src/hooks/useGetCardEventCallback';

import {showToast} from '../../../../../../pages-im/src/utils/customShowToast';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../../../pages-im/src/utils/generalFunction/ubc';

import Portal from '../../../../../Portal';
import {useGetState} from '../../../../hooks';
import type {FeedbackPopupDataProps} from '../../../../index.d';

import FeedbackPopup from './components/FeedbackPopup';
import styles from './index.module.less';
import type {FeedbackBtnProps} from './index.d';
import {
    likestatusEnum,
    type Likestatus,
    type FeedbackPopupData
} from './components/FeedbackPopup/index.type';

const commonClassIconLike = 'wz-pr-30 wz-pl-39';
const commonClassIconDislike = 'wz-pl-30 wz-pr-39';

const LikestatusMap = {
    0: 'unselect',
    1: 'like',
    2: 'disLike'
};

const FeedbackBtn = memo((props: FeedbackBtnProps) => {
    const {msgId = '', feature, ext = {}, sessionId} = props;
    const {onRequest} = useGetCardEventCallback();

    const [showPopup, setShowPopup] = useState(false);
    const [likestatus, setLikeStatus, getLikeStatus] = useGetState<Likestatus>('unselect');
    const [popupData, setPopupData] = useState<FeedbackPopupDataProps>();

    // 喜欢不喜欢数据上报
    const reportFeedbackData = useCallback(
        (data: FeedbackPopupData) => {
            const {interactionInfo = {}, submitData = {}, type} = data || {};
            interactionInfo.params = {
                ...interactionInfo.params,
                bizActionType: 'attitude',
                chatData: {
                    sessionId
                },
                bizActionData: {
                    voteMsgInfo: {
                        msgId,
                        attitude: likestatusEnum[getLikeStatus()],
                        ...submitData
                    }
                }
            };

            onRequest({
                info: interactionInfo
            }).catch(error => {
                const msg = error?.[0]?.msg || '网络异常~';

                showToast({
                    title: msg,
                    icon: 'none',
                    duration: 2000
                });
            });

            ubcCommonClkSend({
                value: type === 'like' ? 'agentReply_like' : 'agentReply_dislike',
                ext: {
                    product_info: {
                        submitData: submitData || {},
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [ext, getLikeStatus, msgId, onRequest, sessionId]
    );

    const handlePopupToast = useCallback(
        (key, eventType) => {
            const actionInfo = props?.feature?.[key]?.actionInfo;

            if (eventType === 'toast') {
                showToast({
                    title: '您的反馈已取消',
                    icon: 'none',
                    duration: 2000
                });
            }

            if (eventType === 'popup' && actionInfo?.interaction === 'popup') {
                setShowPopup(true);
                setPopupData(actionInfo?.interactionInfo?.popupInfo);
            }

            reportFeedbackData({
                type: key,
                interactionInfo: actionInfo?.interactionInfo
            });
        },
        [props?.feature, reportFeedbackData]
    );

    // 点击喜欢不喜欢按钮状态变更
    const handleClickLike = useCallback(
        _btnType => {
            switch (_btnType) {
                case 'WiseLike':
                    setLikeStatus('like');
                    handlePopupToast('like', 'popup');
                    break;

                case 'WiseLikeSolid':
                    setLikeStatus('unselect');
                    handlePopupToast('dislike', 'toast');
                    break;

                case 'WiseDislike':
                    setLikeStatus('disLike');
                    handlePopupToast('dislike', 'popup');
                    break;

                case 'WiseDislikeSolid':
                    setLikeStatus('unselect');
                    handlePopupToast('dislike', 'toast');
                    break;

                default:
                    break;
            }
        },

        [handlePopupToast, setLikeStatus]
    );

    // 根据状态渲染喜欢不喜欢按钮组件
    const memoLiseAndDislike = useMemo(() => {
        // 渲染喜欢按钮组件
        const WiseLikeIcon = (
            <WiseLike
                className={cx(commonClassIconLike)}
                size={54}
                onClick={() => {
                    handleClickLike('WiseLike');
                }}
            />
        );

        // 渲染喜欢按钮组件（选中状态）
        const WiseLikeSolidIcon = (
            <WiseLikeSolid
                className={cx(commonClassIconLike)}
                size={54}
                color='#00C8C8'
                onClick={() => {
                    handleClickLike('WiseLikeSolid');
                }}
            />
        );

        // 渲染不喜欢按钮组件
        const WiseDislikeIcon = (
            <WiseDislike
                className={cx(commonClassIconDislike, styles.aiFeedBackIcon)}
                size={54}
                onClick={() => {
                    handleClickLike('WiseDislike');
                }}
            />
        );

        // 渲染不喜欢按钮组件（选中状态）
        const WiseDislikeSolidIcon = (
            <WiseDislikeSolid
                className={cx(commonClassIconDislike, styles.aiFeedBackIcon)}
                size={54}
                color='#00C8C8'
                onClick={() => {
                    handleClickLike('WiseDislikeSolid');
                }}
            />
        );

        const likeMap = {
            unselect: (
                <>
                    {WiseLikeIcon}
                    {WiseDislikeIcon}
                </>
            ),
            like: (
                <>
                    {WiseLikeSolidIcon}
                    {WiseDislikeIcon}
                </>
            ),
            disLike: (
                <>
                    {WiseLikeIcon}
                    {WiseDislikeSolidIcon}
                </>
            )
        };

        return <View className={cx('wz-flex wz-flex-1 wz-row-center')}>{likeMap[likestatus]}</View>;
    }, [handleClickLike, likestatus]);

    useEffect(() => {
        const likestatus = LikestatusMap[feature?.attributeStatus || ''];
        if (likestatus) {
            setLikeStatus(likestatus);
        }

        ubcCommonViewSend({
            value: 'agentReplyBtn',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View className={cx(styles.aiFeedBack, 'wz-flex wz-rol-center wz-row-between')}>
            {memoLiseAndDislike}
            <Portal>
                <FeedbackPopup
                    reportFeedbackData={reportFeedbackData}
                    showPopup={showPopup}
                    popupData={popupData}
                    likestatus={likestatus}
                    onClosePopup={() => setShowPopup(false)}
                />
            </Portal>
        </View>
    );
});

FeedbackBtn.displayName = 'FeedbackBtn';

export default FeedbackBtn;
