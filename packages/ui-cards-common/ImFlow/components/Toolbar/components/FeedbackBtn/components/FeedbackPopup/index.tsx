/**
 * @file 反馈组件
 * <AUTHOR>
 */
import React, {memo, useState, useCallback, useEffect, Fragment} from 'react';
import {ScrollView, View, Text} from '@tarojs/components';
import {Button, Popup, Textarea} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import type {FeedbackPopupProps} from './index.type';
import styles from './index.module.less';

const MAX_LENGTH = 500;

const FeedbackPopup = memo((props: FeedbackPopupProps) => {
    const {showPopup, onClosePopup, popupData, reportFeedbackData, likestatus} = props;
    const {title, input, content, submitBtn} = popupData?.content || {};
    const [open, setOpen] = useState(showPopup);
    const [reason, setRemarks] = useState('');
    const [checkBoxData, setCheckBoxData] = useState<string[]>([]);

    // 关闭弹窗
    const handleClose = useCallback(() => {
        setOpen(false);
        onClosePopup?.();
    }, [onClosePopup]);

    useEffect(() => {
        setOpen(showPopup);
    }, [showPopup]);

    // 输入框内容更新
    const handleSetDiseaseForm = useCallback((val: string) => {
        // 截取MAX_LENGTH个字符,修复textarea maxlength 不准确的问题
        const newVal = val?.slice(0, MAX_LENGTH);
        setRemarks(newVal);
    }, []);

    // 选中问题表单项
    const handleClickTap = useCallback(
        (selectData: string) => {
            if (checkBoxData.includes(selectData)) {
                const index = checkBoxData.findIndex(item => item === selectData);
                checkBoxData.splice(index, 1);
                setCheckBoxData([...checkBoxData]);

                return;
            }
            setCheckBoxData([...checkBoxData, selectData]);
        },
        [checkBoxData]
    );

    // 提交表单
    const handleClickSubmit = useCallback(() => {
        const {interactionInfo = {}} = submitBtn || {};
        const submitData = {
            reason,
            content: checkBoxData
        };
        reportFeedbackData?.(
            {
                type: likestatus,
                submitData,
                interactionInfo,
                ubcType: 'submit'
            },
            true
        );
        handleClose();
    }, [checkBoxData, handleClose, reason, reportFeedbackData, submitBtn, likestatus]);

    return (
        <Popup
            open={open}
            catchMove={false}
            className={styles.popup}
            placement='bottom'
            rounded
            title={title}
            style={{maxHeight: '80vh', minHeight: '70vh', zIndex: 1003}}
            titleStyle={{border: 0}}
            onClose={handleClose}
        >
            <Popup.Close />
            <Popup.Backdrop onClick={e => e.stopPropagation()} />
            <View className={cx(styles.feebackMain, 'wz-plr-51')}>
                <ScrollView scrollY className={cx(styles.scrollView)}>
                    <View
                        className={cx(
                            styles.feebackMainCheckBox,
                            'wz-flex wz-row-between wz-flex-wrap wz-mt-39'
                        )}
                    >
                        {content?.map(item => {
                            return (
                                <Fragment key={item?.subTitle}>
                                    {item?.items?.map(obj => {
                                        return (
                                            <View
                                                key={obj?.value}
                                                className={cx(
                                                    styles.feebackMainCheckBoxItem,
                                                    'wz-fs-48 wz-flex wz-row-center wz-col-center wz-mb-30',
                                                    checkBoxData.includes(obj?.value)
                                                        ? styles.selected
                                                        : '',
                                                    checkBoxData.includes(obj?.value)
                                                        ? 'wz-fw-700'
                                                        : ''
                                                )}
                                                onClick={() => {
                                                    handleClickTap(obj?.value);
                                                }}
                                            >
                                                {obj?.value || ''}
                                            </View>
                                        );
                                    })}
                                </Fragment>
                            );
                        })}
                    </View>

                    <View className={styles.diseaseFillTextarea}>
                        <View className='wz-br-45 wz-mb-63 wz-fs-42'>
                            <View className={cx(styles.textareaBox, 'wz-br-36')}>
                                <Textarea
                                    className={cx(styles.textareaCon, 'wz-br-36 wz-fs-42')}
                                    name='content'
                                    placeholder={input?.placeholder || ''}
                                    placeholderClass={styles.textareaPlaceholder}
                                    maxlength={MAX_LENGTH}
                                    value={reason}
                                    autoHeight={false}
                                    cursor-spacing='20'
                                    adjustPosition
                                    onInput={e => {
                                        handleSetDiseaseForm(e?.detail?.value);
                                    }}
                                />
                                <View className={cx(styles.textareaCount, 'wz-fs-42')}>
                                    <Text>{reason?.length || 0}</Text>/{MAX_LENGTH}
                                </View>
                            </View>
                        </View>
                    </View>
                </ScrollView>
            </View>

            <Popup.Button>
                <View className='footer'>
                    <View className='line' />
                    <View className='wz-flex wz-fs-48 wz-ptb-24 wz-plr-51'>
                        <Button
                            className={cx(styles.footerBtn, 'wz-flex-1')}
                            color='primary'
                            shape='round'
                            onClick={handleClickSubmit}
                        >
                            <Text className='wz-fs-54 wz-fw-500'>{submitBtn?.value}</Text>
                        </Button>
                    </View>
                </View>
            </Popup.Button>
        </Popup>
    );
});

FeedbackPopup.displayName = 'FeedbackPopup';

export default FeedbackPopup;
