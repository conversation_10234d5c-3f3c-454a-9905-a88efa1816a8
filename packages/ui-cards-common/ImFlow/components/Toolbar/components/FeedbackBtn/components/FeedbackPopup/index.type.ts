import type {FeedbackPopupDataProps, InteractionInfo} from '@typings/im.d';

// eslint-disable-next-line no-shadow
export const likestatusEnum = {
    unselect: '3',
    like: '1',
    disLike: '2'
} as const;

export type Likestatus = keyof typeof likestatusEnum;

export interface FeedbackPopupData {
    type: Likestatus;
    interactionInfo: InteractionInfo;
    submitData?: {
        content?: string[];
        reason?: string;
        attitude?: (typeof likestatusEnum)[keyof typeof likestatusEnum];
    };
    ubcType?: string;
}
export interface FeedbackPopupProps {
    showPopup: boolean;
    onClosePopup: () => void;
    popupData: FeedbackPopupDataProps | undefined;
    likestatus: Likestatus;
    reportFeedbackData: (
        _data: FeedbackPopupData,
        showSubmitToast?: boolean,
        ubcType?: string
    ) => void;
}
