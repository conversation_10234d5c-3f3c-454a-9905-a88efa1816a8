import cx from 'classnames';
import {eventCenter, vibrateShort} from '@tarojs/taro';
import {View, Image} from '@tarojs/components';
import {memo, useCallback, useEffect, useMemo, useState} from 'react';

import {useGetCardEventCallback} from '../../../../../../../pages-im/src/hooks/useGetCardEventCallback';
import {getExperienceConfig} from '../../../../../../../pages-im/src/store/triageStreamAtom/otherData';

import {showToast} from '../../../../../../../pages-im/src/utils/customShowToast';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../../../../pages-im/src/utils/generalFunction/ubc';

import Portal from '../../../../../../Portal';
import {useGetState} from '../../../../../hooks';
import type {FeedbackPopupDataProps} from '../../../../../index.d';

import FeedbackPopup from '../components/FeedbackPopup';
import {disLikeIcon, disLikeSolid, likeIcon, likeIconSolid} from '../../../../../constants';
import {
    likestatusEnum,
    type Likestatus,
    type FeedbackPopupData
} from '../components/FeedbackPopup/index.type';
import type {FeedbackBtnProps} from '../index.d';
import styles from './index.module.less';

const LikestatusMap = {
    0: 'unselect',
    1: 'like',
    2: 'disLike'
};

const FeedbackBtnV2 = memo((props: FeedbackBtnProps) => {
    const {msgId = '', feature, ext = {}, sessionId = '', showToolbarTools} = props;
    const {onRequest} = useGetCardEventCallback();
    const experienceConfig = getExperienceConfig();
    // 是否支持点赞弹窗，0：不支持，1：支持
    const supportLike = experienceConfig?.supportLike || 0;

    const [showPopup, setShowPopup] = useState(false);
    const [likestatus, setLikeStatus, getLikeStatus] = useGetState<Likestatus>('unselect');
    const [popupData, setPopupData] = useState<FeedbackPopupDataProps>();

    // 点踩数据上报
    const reportFeedbackData = useCallback(
        (data: FeedbackPopupData, showSubmitToast?: boolean) => {
            const {interactionInfo = {}, submitData = {}, ubcType} = data || {};
            interactionInfo.params = {
                ...interactionInfo.params,
                bizActionType: 'attitude',
                chatData: {
                    sessionId
                },
                bizActionData: {
                    voteMsgInfo: {
                        msgId,
                        attitude: likestatusEnum[getLikeStatus()],
                        ...submitData
                    }
                }
            };

            onRequest({
                info: interactionInfo
            })
                .then(() => {
                    showSubmitToast &&
                        showToast({
                            title: '感谢您的反馈',
                            icon: 'success'
                        });
                })
                .catch(error => {
                    const msg = error?.[0]?.msg || '网络异常~';

                    showToast({
                        title: msg,
                        icon: 'none',
                        duration: 2000
                    });
                });
            ubcType === 'submit' &&
                ubcCommonClkSend({
                    value: `agentReply_${data?.type?.toLowerCase() || 'dislike'}`,
                    ext: {
                        value_type: ubcType,
                        product_info: {
                            msgId,
                            ...ext
                        }
                    }
                });
        },
        [ext, getLikeStatus, msgId, onRequest, sessionId]
    );

    const handlePopupToast = useCallback(
        (key, eventType) => {
            const actionInfo = props?.feature?.[key]?.actionInfo;
            // 加抖动效果
            vibrateShort({type: 'light'});

            if (eventType === 'toast') {
                showToast({
                    title: '您的反馈已取消',
                    icon: 'none',
                    duration: 2000
                });
            }

            if (eventType === 'popup' && actionInfo?.interaction === 'popup') {
                setShowPopup(true);
                setPopupData(actionInfo?.interactionInfo?.popupInfo);
                ubcCommonViewSend({
                    value: `agentReply_popup_${key}`,
                    ext: {
                        product_info: {
                            msgId,
                            ...ext
                        }
                    }
                });
            }

            reportFeedbackData({
                type: key,
                interactionInfo: actionInfo?.interactionInfo
            });
        },
        [ext, msgId, props?.feature, reportFeedbackData]
    );

    // 点击不喜欢按钮状态变更
    const handleClickLike = useCallback(
        _btnType => {
            switch (_btnType) {
                case 'WiseLike':
                    setLikeStatus('like');
                    handlePopupToast('like', 'popup');
                    eventCenter.trigger(`updateLikeStatus_${msgId}_${sessionId}`, 'like');
                    break;

                case 'WiseLikeSolid':
                    setLikeStatus('unselect');
                    handlePopupToast('like', 'toast');
                    eventCenter.trigger(`updateLikeStatus_${msgId}_${sessionId}`, 'unselect');
                    break;

                case 'WiseDislike':
                    setLikeStatus('disLike');
                    handlePopupToast('dislike', 'popup');
                    eventCenter.trigger(`updateLikeStatus_${msgId}_${sessionId}`, 'disLike');
                    break;

                case 'WiseDislikeSolid':
                    setLikeStatus('unselect');
                    handlePopupToast('dislike', 'toast');
                    eventCenter.trigger(`updateLikeStatus_${msgId}_${sessionId}`, 'unselect');
                    break;

                default:
                    break;
            }
        },

        [handlePopupToast, msgId, sessionId, setLikeStatus]
    );

    useEffect(() => {
        eventCenter.on(`onLikeClick_${msgId}_${sessionId}`, handleClickLike);
        return () => {
            eventCenter.off(`onLikeClick_${msgId}_${sessionId}`, handleClickLike);
        };
    }, [handleClickLike, msgId, sessionId]);

    // 根据状态渲染喜欢不喜欢按钮组件
    const memoLikeAndDislike = useMemo(() => {
        // 喜欢按钮组件
        const WiseLikeIcon = <Image className={styles.dislikeIcon} src={likeIcon} />;

        // 喜欢按钮组件（选中状态）
        const WiseLikeSolidIcon = <Image className={styles.dislikeIcon} src={likeIconSolid} />;
        // 渲染不喜欢按钮组件
        const WiseDislikeIcon = <Image className={styles.dislikeIcon} src={disLikeIcon} />;

        // 渲染不喜欢按钮组件（选中状态）
        const WiseDislikeSolidIcon = <Image className={styles.dislikeIcon} src={disLikeSolid} />;

        const likeMap = supportLike
            ? {
                unselect: [
                    {
                        type: 'WiseLike',
                        comp: WiseLikeIcon,
                        ubcValue: 'like'
                    },
                    {
                        type: 'WiseDislike',
                        comp: WiseDislikeIcon,
                        ubcValue: 'dislike'
                    }
                ],
                like: [
                    {
                        type: 'WiseLikeSolid',
                        comp: WiseLikeSolidIcon,
                        ubcValue: 'like'
                    },
                    {
                        type: 'WiseDislike',
                        comp: WiseDislikeIcon,
                        ubcValue: 'dislike'
                    }
                ],
                disLike: [
                    {
                        type: 'WiseLike',
                        comp: WiseLikeIcon,
                        ubcValue: 'like'
                    },
                    {
                        type: 'WiseDislikeSolid',
                        comp: WiseDislikeSolidIcon,
                        ubcValue: 'dislike'
                    }
                ]
            }
            : {
                unselect: [
                    {
                        type: 'WiseDislike',
                        comp: WiseDislikeIcon,
                        ubcValue: 'like'
                    }
                ],
                disLike: [
                    {
                        type: 'WiseDislikeSolid',
                        comp: WiseDislikeSolidIcon,
                        ubcValue: 'dislike'
                    }
                ]
            };

        return (
            <View
                className={cx('wz-flex wz-flex-1 wz-row-center', {
                    [styles.unlikeAnimate]: likestatus === 'disLike'
                })}
            >
                {likeMap[likestatus]?.map((item, index) => {
                    return (
                        <View
                            key={`icon_${index}`}
                            className={cx(
                                styles.iconBg,
                                'wz-flex wz-flex-1 wz-row-center wz-mr-45'
                            )}
                            onClick={() => {
                                handleClickLike(item.type);
                                // 按钮点击事件上报挪到点击事件里面，防止受trigger影响
                                ubcCommonClkSend({
                                    value: `agentReply_${item.ubcValue}`,
                                    ext: {
                                        product_info: {
                                            submitData: {},
                                            msgId,
                                            ...ext
                                        }
                                    }
                                });
                            }}
                        >
                            {item?.comp}
                        </View>
                    );
                })}
            </View>
        );
    }, [ext, handleClickLike, likestatus, msgId, supportLike]);

    useEffect(() => {
        const likestatus = LikestatusMap[feature?.attributeStatus || ''];
        if (likestatus) {
            setLikeStatus(likestatus);
            eventCenter.trigger(`updateLikeStatus_${msgId}_${sessionId}`, likestatus);
        }

        ubcCommonViewSend({
            value: 'agentReplyBtn',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            {showToolbarTools && feature?.dislike && (
                <View className={cx(styles.toolIcon, 'wz-flex wz-rol-center wz-row-center')}>
                    {memoLikeAndDislike}
                </View>
            )}
            <Portal>
                <FeedbackPopup
                    reportFeedbackData={reportFeedbackData}
                    showPopup={showPopup}
                    popupData={popupData}
                    likestatus={likestatus}
                    onClosePopup={() => setShowPopup(false)}
                />
            </Portal>
        </>
    );
});

FeedbackBtnV2.displayName = 'FeedbackBtnV2';

export default FeedbackBtnV2;
