export interface TTSText {
    data: string;
    type: string;
    index: number;
}

export interface VoiceBtnProps {
    voiceInfo: {
        speaker: string;
        params?: InitParams;
    };
    voiceContent?: string;
    msgId?: string;
    sessionId?: string;
    showVoice?: boolean;
}

export interface InitParams {
    commonParams?: {
        appName: string;
        appToken: string;
    };
    tts?: {
        audioCtrl: string;
        aue: number;
        per: number;
        pit: number;
        spd: number;
        vol: number;
    };
    sessionId?: string;
    msgId?: string;
}

export interface TTSOptions {
    params?: InitParams;
    content: TTSText[];
    speaker?: string;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: any) => void;
}

export interface Message {
    msgKey: string;
    msgStatus: number;
    message: string;
}

export enum IServiceHandlerType {
    TTS_START = 'TTS_START',
    TTS_STOP = 'TTS_STOP',
    TTS_ERROR = 'TTS_ERROR',
    TTS_END = 'TTS_END',
    TTS_PRELOAD_FINISHED = 'TTS_PRELOAD_FINISHED'
}

export interface IHandlerCallbackNode {
    onError: (...args: any[]) => void;
    onPlay: (...args: any[]) => void;
    onEnd: (...args: any[]) => void;
}
