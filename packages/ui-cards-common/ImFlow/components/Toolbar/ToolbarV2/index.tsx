import {memo, useMemo, useCallback, useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import {eventCenter} from '@tarojs/taro';

import {ToolBarProps} from '../index.d';
import CopyBtn from '../components/CopyBtn';
import VoiceBroadcastBtn from '../components/VoiceBroadcastBtn';
import FeedbackBtnV2 from '../components/FeedbackBtn/FeedbackBtnV2';
import {getExperienceConfig} from '../../../../../pages-im/src/store/triageStreamAtom/otherData';

const ToolbarV2 = memo((props: ToolBarProps) => {
    const {msgData, page, showToolbarTools, isMsgEnd} = props;
    const {msgId = '', data, sessionId = ''} = msgData || {};
    const {feature, content} = data || {};
    const {ext} = content?.data || {};
    const experienceConfig = getExperienceConfig();
    // 是否支持h5语音播报，0：不支持，1：支持
    const {supportTTsForH5 = 0} = experienceConfig || {};
    // h5语音播报实验中
    const isShowTTS = page !== 'docIm' && (process.env.TARO_ENV !== 'h5' || supportTTsForH5);
    const [tabIndex, setTabIndex] = useState(0);

    // TODO: 跟外层方法合并@gongjianmei
    const convertData = useMemo(() => {
        // todo暂取最后一段content(待确认)
        const originList = data?.content?.data?.content?.list || [];
        const targetContent = originList[originList.length - 1] || {};
        const {content, tabList, contentList} = targetContent;
        if (content && !tabList) {
            return content;
        }
        let tabContent = '';
        if (tabList) {
            const {header, body} = tabList;
            if (header?.length) {
                tabContent += header.map(item => `${item.title}：${item.content}\n`).join('');
            }
            if (body?.length) {
                const {title = '', list = []} = body[tabIndex];
                tabContent += `\n${title}\n`;
                tabContent += list.map(item => `\n${item.title}\n${item.content}`).join('');
            }
        }

        if (contentList) {
            contentList?.ids.forEach(id => {
                const item = contentList?.data?.[id];
                if (item?.component === 'markdown') {
                    tabContent += `\n${item?.contentAdd}\n`;
                }
            });
        }

        if (content) {
            tabContent += `\n${content}`;
        }
        return tabContent || '';
    }, [data?.content?.data?.content?.list, tabIndex]);

    const changeTab = useCallback(async (index: number) => {
        setTabIndex(index);
        eventCenter.trigger('stopTTS');
    }, []);

    useEffect(() => {
        eventCenter.on('changeMsgTab', changeTab);
        return () => {
            eventCenter.off('changeMsgTab', changeTab);
        };
    }, [changeTab]);

    return (
        <View className={showToolbarTools ? 'wz-flex wz-pb-45 wz-row-between' : ''}>
            <View className={'wz-flex'}>
                {feature?.copyInfo && showToolbarTools ? (
                    <CopyBtn copyInfo={feature?.copyInfo} copyContent={convertData} />
                ) : null}
                {feature?.dislike ? (
                    <FeedbackBtnV2
                        feature={feature}
                        msgId={msgId}
                        sessionId={sessionId}
                        ext={ext}
                        showToolbarTools={showToolbarTools}
                    />
                ) : null}
            </View>
            {/* 小程序版本的语音播报全量，h5实验中 */}
            {feature?.tts && isShowTTS && isMsgEnd ? (
                <VoiceBroadcastBtn
                    showVoice={feature?.tts && isShowTTS && showToolbarTools}
                    voiceInfo={feature?.tts}
                    voiceContent={convertData}
                    msgId={msgId}
                    sessionId={sessionId}
                />
            ) : null}
        </View>
    );
});

ToolbarV2.displayName = 'ToolbarV2';
export default ToolbarV2;
