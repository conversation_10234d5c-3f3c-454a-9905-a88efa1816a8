/**
 * 切割超过限定字数的文本
 *
 * @param text - 语音播报文案
 * @param maxLength - 限制字数
 * @returns 切割后语音播报新数据
 */
export const splitLongText = (text: string, maxLength: number) => {
    const result = [] as string[];
    let currentText = text;
    while (currentText.length > maxLength) {
        result.push(currentText.substring(0, maxLength));
        currentText = currentText.substring(maxLength);
    }
    if (currentText.trim() !== '') {
        result.push(currentText);
    }
    return result;
};

/**
 * 转化语音播报数据
 *
 * @param contents - 语音播报数据
 * @returns 转化后语音播报新数据
 */
export const getVoiceTexts = (contents: string) => {
    // markdown解析ttsInfo
    // 得到最终的markdown文本
    const ttsString: string = contents;

    const markdownTextArray = splitMarkdown(ttsString);
    const voiceText = markdownTextArray
        ?.map(text => {
            const res =
                text?.replace(/(\n)+/g, '')?.replace(/<sup[^>]*>([\s\S]*?)<\/sup>/g, '') || text;
            // 基于当前音色转化目前限制50字 切割超过50字的文本 剩余部分添加到下一项
            if (res.length > 50) {
                return splitLongText(res, 50);
            }
            return res;
        })
        ?.flat()
        ?.filter(item => item.trim() !== '');
    const voiceInfo = voiceText.map((item, index) => ({
        data: item,
        type: 'text',
        index: index
    }));
    return voiceInfo;
};

/**
 * 将嵌套数组扁平化为一维数组
 *
 * @param arr - 待扁平化的数组
 * @returns 扁平化后的一维数组
 */
export function flattenArray(arr: string[]): string[] {
    const flattened: string[] = [];
    const stack: string[] = [...arr];

    while (stack.length) {
        const item = stack.pop() as string[] | string;

        if (Array.isArray(item)) {
            stack.push(...item);
        } else {
            flattened.unshift(item);
        }
    }

    return flattened;
}

export const splitMarkdown = markdownString => {
    if (typeof markdownString !== 'string') {
        return markdownString;
    }

    let blocks = markdownString
        .split('\n')
        .filter?.(i => !!i)
        .filter(i => i !== '\n');

    blocks = blocks.map((item, index) => {
        if (index >= blocks.length - 1) {
            return item;
        }
        return `${item}\n\n`;
    });
    const block2inlines = blocks?.map?.(item => {
        // 包含A标签的项，不进行切割
        const execH1 = /^\s*\*\*(.+)\*\*\s*(\n\n)?$/.exec(item);
        const execLink = /\[.+\]\(.+\)/g.exec(item);

        if (execLink || execH1) {
            return item;
        }
        return item;
    });
    return flattenArray(block2inlines)?.filter?.(i => (i as string)?.trim() !== '') || [];
};
