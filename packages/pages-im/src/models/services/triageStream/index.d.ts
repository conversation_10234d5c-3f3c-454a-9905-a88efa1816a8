import type {MsgId, InteractionInfo, Qid} from '../../../typings';
import type {
    MsgItemType,
    SessionId,
    CapsulesToolsType,
    UserDataType,
    InputDataType,
    StatusDataType,
    NpsConfig
} from '../../../store/triageStreamAtom/index.type.ts';

export interface GetTriageStreamCoreParamsType {
    riskInfo?: unknown;
    ctrlData?: {
        newSession?: 1 | 0;
        pageFrom?: 'sessionList' | 'pageRefresh';
        tmpBotVersion2?: 1 | 0; // 是否为新版本，用于 cbot2.0 上线过程兼容；
        currentSessionId?: SessionId;
        ctrlType?: 'uploadImgReport' | 'uploadImgSkin'; // 自动弹出弹层，上传类型
        showTab?: 1 | 0; // 1:展示tab 0:隐藏tab
    };
}

export interface AgreementDataType {
    list: {
        type: 'highLightText' | 'text';
        value: string;
        interaction: string;
        interactionInfo: InteractionInfo;
    }[][];
}

export interface GetTriageStreamCoreRespType {
    toolData: {
        capsules: CapsulesToolsType[];
    };
    msgIds?: MsgId[];
    msgData?: {
        [k in MsgId]: MsgItemType<unknown>;
    };
    session: {
        sessionId: SessionId;
        isPullHistory: boolean;
        hasMorePreMsg: boolean;
        toast?: string;
        reUseSession?: boolean;
    };
    userData: {
        avatar: string;
        isLogin: boolean;
        name: string;
    };
    titleData: {
        title: string;
        titleTips?: string;
        showHistoryEntry: boolean; // 历史会话
        showImCreateEntry?: boolean; // 新增会话
    };
    inputData: InputDataType;
    imActionData?: {
        popData?: InputDataType;
        actionData: {
            actionType: 'antipass';
            interaction: 'popup';
            interactionInfo: InteractionInfo;
        };
    };
    statusData?: StatusDataType;
    bottomTips?: {
        agreement?: AgreementDataType;
    };
}

interface PatientInfo {
    contactId?: string;
    gender?: string;
    age?: string;
    telphone?: string;
}

interface SwitchCouponInfo {
    msgId: MsgId;
    couponId: number;
}

export interface EditedZhusuInfo {
    msgId: MsgId;
    patientInfo: PatientInfo;
    zhusu?: string;
    telPhone?: string;
    couponId?: string;
    skuId?: string;
}

interface StopConvInfoType {
    msgId: MsgId;
    eventId: string;
    reason: string;
}

interface ChatData {
    expertId?: number;
    sessionId: SessionId;
    qid?: Qid;
    content?: string;
    chatType?: string;
    provCode?: string;
    cityCode?: string;
}

export interface GetUseractionParamsType<T extends CommonBizActionType> {
    chatData: ChatData;
    bizActionType: T;
    bizActionData: RequiredBizActionData[T];
}

export interface GetUserBizActionParamsType<T extends WzBizActionType> {
    chatData: ChatData;
    bizActionType: T;
    bizActionData: RequiredBizActionData[T];
}

export type CommonBizActionType =
    | 'stopConv' // 打断
    | 'attitude'; // 赞踩

export type WzBizActionType =
    | 'stopConv' // 打断
    | 'capsuleClick' // 点击胶囊
    | 'userLogin' // 登录
    | 'editZhusu' // 编辑主诉
    | 'switchCoupon'; // 切换优惠券

export interface userInfoItem {
    msgId: MsgId;
    patientInfo?: PatientInfo;
}

export interface RequiredBizActionData {
    stopConv: {stopConvInfo: StopConvInfoType};
    attitude: {
        voteMsgInfo: {
            msgId: MsgId;
            attitude: string;
            reason: string;
            content: string;
        };
    };
    capsuleClick: {
        clickCapsuleInfo: {
            msgKey: MsgId;
            content: string;
        };
    };
    userLogin: {userLoginInfo?: userInfoItem; userLoginInfoList?: {msgList: userInfoItem[]}};
    editZhusu: {editZhusuInfo: EditedZhusuInfo};
    switchCoupon: {switchCouponInfo: SwitchCouponInfo};
}

type commonData = {
    chatData: ChatData;
    userData: UserDataType;
    toolData?: {
        capsules: {
            list: CapsulesToolsType[];
            md5: string;
        };
    };
    statusData: StatusDataType;
};

type MessageAndData = commonData & {
    message: MsgItemType<unknown>[];
};

export type GetUseractionRespType<T extends CommonBizActionType> = T extends 'stopConv' | 'attitude'
    ? commonData // 只包含 chatData 和 userData
    : MessageAndData;

export type GetUserBizActionRespType<T extends WzBizActionType> = T extends 'stopConv'
    ? commonData
    : MessageAndData;

interface LongPressConfigItem {
    type?: string;
    text?: string;
    icon?: string;
    selectedText?: string;
    selectedIcon?: string;
}
export interface ExperienceConfig {
    // 支持点赞
    supportLike?: 0 | 1;
    // h5支持tts
    supportTTsForH5?: 0 | 1;
    // 历史消息下拉刷新
    pullDownHistoryMsg?: 0 | 1;
    // bot回复长按配置
    botLongPress?: LongPressConfigItem[];
    // 用户消息长按配置
    userLongPress?: LongPressConfigItem[];
}
export interface GetOtherDataRespType {
    statusData: StatusDataType;
    configData?: {
        // nps 相关配置
        npsConfig?: NpsConfig;
        // 体验相关配置
        experienceConfig?: ExperienceConfig;
    };
}

// TODO：明确是否性能影响；@wanghaoyu08
// 定义数字元组类型
type Tuple<T, N extends number> = N extends N
    ? number extends N
        ? T[]
        : _TupleOf<T, N, []>
    : never;
type _TupleOf<T, N extends number, R extends unknown[]> = R['length'] extends N
    ? R
    : _TupleOf<T, N, [T, ...R]>;

// 获取小于N的数字联合类型
type LessThan<N extends number> = Exclude<Tuple<number, N>[number], N>;

export interface GetSessionMsgListParamsType {
    size: LessThan<11>;
    sessionId: SessionId;
    viewType: 'next' | 'pre';
    currentMsgId?: MsgId;
}

export interface GetSessionMsgListRespType {
    msgData: {
        [k in MsgId]: MsgItemType<unknown>;
    };
    msgIds: MsgId[];
    hasMore: boolean;
}
