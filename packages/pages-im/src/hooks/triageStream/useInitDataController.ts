import {useSet<PERSON><PERSON>, useAtom} from 'jotai';
import {useCallback, useRef, useState, useEffect} from 'react';
import {hideLoading, nextTick, eventCenter} from '@tarojs/taro';

import {
    userDataAtom,
    titleDataAtom,
    inputData<PERSON>tom,
    curSessionIdAtom,
    updateDataForUbc<PERSON>tom,
    lastMsgIdAtom,
    switchSession as switchSessionAction,
    resetTriageStreamMsgIdsAtom,
    popDataAtom
} from '../../store/triageStreamAtom';
import {
    updateCurSessionMsgDataAndMsgIds,
    updateTriageSessionHasHistoryMsgAtom
} from '../../store/triageStreamAtom/msg';
import {
    ongoingToastAtom,
    titleInfoAtom,
    viewOrderInfoAtom,
    agreementInfoAtom,
    showBotEntranceCardAtom,
    npsConfigAtom,
    experienceConfigAtom
} from '../../store/triageStreamAtom/otherData';

import {isEmpty} from '../../utils';
import {showToast} from '../../utils/customShowToast';
import {getFkParamsAll, type FkParamsVal} from '../../utils/generalFunction/riskControler';
import {useHandleUserLoginBizAction} from '../../hooks/triageStream/useHandleUserBizAction';

import {
    getTriageStreamCoreReq,
    getOtherDataReq,
    getSessionMsgListReq
} from '../../models/services/triageStream';
import type {
    GetTriageStreamCoreParamsType,
    GetTriageStreamCoreRespType
} from '../../models/services/triageStream/index.d';
import {ICMenuProps} from '../../components/CMenu/index.d';

import {
    MSG_KEY_LENGTH,
    // MSG_THINKING_MSG_KEY_PREFIX,
    type MSG_CARDID_ENUM_STREAM_TYPE
} from '../../constants/msg';

import type {MsgId} from '../../typings';
import type {MsgItemType, SessionId} from '../../store/triageStreamAtom/index.type.ts';

import {useGetUrlParams} from '../common';
import {useGetSessionMsgIds} from './dataController';
import {updateSpecialCardAdjectiveMsgId} from './msgUtils';
import {useGetSwanMsgListSceneStatus} from './useGetSwanMsgListSceneStatus';
import {useConversationDataController} from './useConversationDataController';

import type {AntiPassDataType} from './index.d';

/**
 * 检查字符串是否为 genImMsgKey 生成的 msgKey
 * @param {string} str
 * @returns {boolean}
 */
export const isGenImMsgKey = (str: string) => {
    return new RegExp(`^[0-9a-zA-Z]{${MSG_KEY_LENGTH}}$`).test(str);
};

/**
 * 获取核心数据
 *
 * @returns 如果请求成功，则返回核心数据；如果请求失败，则返回undefined
 */
const getCoreData = async (arg: {
    fkParams: FkParamsVal | undefined;
    ctrlData?: GetTriageStreamCoreParamsType['ctrlData'];
}) => {
    const {fkParams, ctrlData} = arg;

    const [err, data] = await getTriageStreamCoreReq({
        riskInfo: Object.assign(
            {},
            fkParams
                ? {
                    ...(fkParams as object),
                    v: ''
                }
                : {}
        ),
        ...(ctrlData ? {ctrlData} : {})
    });
    if (!err) {
        return data?.data;
    }
};

/**
 * 获取风控参数
 *
 * @returns 风控参数
 */
const getFkParams = async () => {
    let fkRes: FkParamsVal | null = null;
    if (!fkRes && process.env.TARO_ENV === 'h5') {
        fkRes = await getFkParamsAll({
            ak: '6810',
            ev: 'page_enter',
            dataApp:
                // eslint-disable-next-line
                'eyJhcHBfa2V5IjoiNjgxMCIsImFwcF92aWV3IjoicHJvbW90ZSIsImJyb3dzZXJfdXJsIjoiaHR0cHM6Ly9zb2ZpcmUuYmFpZHUuY29tL2RhdGEvdWEvYWIuanNvbiIsImZvcm1fZGVzYyI6IiIsInNlbmRfaW50ZXJ2YWwiOjUwLCJzZW5kX21ldGhvZCI6M30='
        });

        return fkRes;
    }

    return undefined;
};

/**
 * 获取页面其他数据
 *
 * @returns 如果请求成功，则返回其他数据；如果请求失败，则返回undefined
 */
const getOtherDataFn = async (arg: {sessionId: SessionId; ctrlData: {showTab: number}}) => {
    const [err, data] = await getOtherDataReq({
        sessionId: arg.sessionId,
        ctrlData: arg.ctrlData
    });

    if (!err) {
        return data?.data;
    }
};

const getHistoryMsgFn = async (arg: {sessionId: SessionId; currentMsgId?: MsgId}) => {
    const [err, data] = await getSessionMsgListReq({
        size: 10,
        sessionId: arg.sessionId,
        viewType: 'pre',
        ...(arg.currentMsgId ? {currentMsgId: arg.currentMsgId} : {})
    });

    if (!err) {
        return data?.data;
    }
};

/**
 * 初始化页面数据控制器
 *
 * @param id 会话ID
 * @returns 无返回值
 */
export const useInitDataController = () => {
    const [canRender, setCanRender] = useState(false);
    const [antiPassData, setAntiPassData] = useState<AntiPassDataType>();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();

    const sessionId = useRef<SessionId>();
    const hasLoadedDataRef = useRef(false); // 是否已经加载过数据, 用于处理页面回退场景下，重复触发 didShow 事件； @wanghaoyu08
    const getDataTriggeredRef = useRef(false);
    const isCreateSessionSceneRef = useRef(false);
    const initiatedSessionIdRef = useRef<SessionId | null>(null);
    const pageFromRef =
        useRef<NonNullable<GetTriageStreamCoreParamsType['ctrlData']>['pageFrom']>(undefined);
    // 首次会话加载控制器；
    const holdPromiseRef = useRef<{promise: Promise<void>; resolve: (() => void) | null} | null>(
        null
    );

    const query = useGetUrlParams();
    const {
        refreshSymbol,
        word: urlWord,
        query: urlQuery,
        sessionId: urlSessionId,
        ctrlType: urlCtrlType,
        intent: urlIntent
    } = query;
    const {msgIds} = useGetSessionMsgIds();
    const {createConversation} = useConversationDataController();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();

    const setUserData = useSetAtom(userDataAtom);
    const setTitleData = useSetAtom(titleDataAtom);
    const setInputData = useSetAtom(inputDataAtom);
    const setPopData = useSetAtom(popDataAtom);
    const setTitleInfo = useSetAtom(titleInfoAtom);
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);
    const setOngoingToast = useSetAtom(ongoingToastAtom);
    const setShowBotEntranceCard = useSetAtom(showBotEntranceCardAtom);
    const setViewOrderInfo = useSetAtom(viewOrderInfoAtom);
    const setAgreementInfo = useSetAtom(agreementInfoAtom);
    const setNpsConfig = useSetAtom(npsConfigAtom);
    const setExperienceConfig = useSetAtom(experienceConfigAtom);
    const [curSessionId, setCurSessionAtom] = useAtom(curSessionIdAtom);
    /**
     *
     * @description 创建一个稳定的 Promise 变量，用于控制 conversion 数据加载执行逻辑。
     *
     */
    const createHoldPromise = () => {
        let resolveFn: (() => void) | null = null;
        const promise = new Promise<void>(resolve => {
            resolveFn = resolve;
        });

        holdPromiseRef.current = {promise, resolve: resolveFn};
    };

    /**
     *
     * @description 释放首次会话加载控制器，getFirstConversation 继续执行;
     */
    const releaseFirstConversation = () => {
        if (holdPromiseRef.current?.resolve) {
            holdPromiseRef.current.resolve(); // 解析 Promise 使 getFirstConversation 继续执行
            holdPromiseRef.current = null; // 清空引用，防止重复调用
        }
    };

    const createMsgAtom = useCallback(
        (
            ids: MsgId[],
            msgData: Record<MsgId, MsgItemType<unknown>>,
            ops: {
                type: 'unshift' | 'push';
                isFirstAutoPull?: boolean;
                triggerSymbol?: 'getCoreData' | 'getHistoryMsg';
                sourceApi?: 'conversation' | 'getHistory';
                extData?: {
                    hasHistoryMsg?: boolean;
                    isPullHistory?: boolean;
                };
            }
        ) => {
            if (!sessionId.current && !curSessionId) return;

            const targetSessionId = sessionId.current || curSessionId || '';
            updateCurSessionMsgDataAndMsgIds({ids, msgData, ops, targetSessionId});

            // 新增消息或者首次拉取历史消息场景下，更新特殊消息卡片有效状态；
            const newIds = [...ids];
            if (ops.type === 'unshift' && !ops.isFirstAutoPull) {
                newIds.reverse();
            }
            newIds.forEach(id => {
                updateSpecialCardAdjectiveMsgId(
                    msgData[id]?.data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE,
                    msgData[id]?.meta.msgId,
                    {type: ops.isFirstAutoPull ? 'push' : ops.type}
                );
            });
            (ops.type === 'push' || ops.isFirstAutoPull) && updateLastMsgId(ids[ids.length - 1]);
        },
        [curSessionId, updateLastMsgId]
    );

    // const updatePagesStackSwitchSceneMsgData = useCallback(
    //     async (arg: {sessionId: SessionId; currentMsgId?: MsgId}) => {
    //         if (!sessionId) return;

    //         const [err, data] = await getSessionMsgListReq({
    //             size: 20,
    //             sessionId: arg.sessionId,
    //             viewType: 'next',
    //             currentMsgId: arg.currentMsgId
    //         });

    //         if (!err) {
    //             const {msgData, msgIds} = data?.data || {};

    //             updateCurSessionMsgIdsAtom([], {
    //                 type: 'deleteAfter',
    //                 targetId: arg.currentMsgId
    //             });

    //             nextTick(() => {
    //                 msgIds?.length &&
    //                     msgData &&
    //                     createMsgAtom([...msgIds.reverse()], msgData, {
    //                         type: 'push',
    //                         isFirstAutoPull: false,
    //                         triggerSymbol: 'getHistoryMsg'
    //                     });
    //             });
    //         }
    //     },
    //     [createMsgAtom]
    // );

    const getOtherData = useCallback(
        async (arg: {sessionId: SessionId; ctrlData: {showTab: number}}) => {
            const res = await getOtherDataFn({
                sessionId: arg.sessionId,
                ctrlData: arg.ctrlData
            });

            if (res?.statusData) {
                const {statusData} = res;

                const {ongoingToast} = statusData?.topTips?.orderGuideTip || {};
                const {isShowBotEntranceCard} = statusData?.popTips || {};
                ongoingToast && setOngoingToast(ongoingToast);
                isShowBotEntranceCard && setShowBotEntranceCard(isShowBotEntranceCard);
            }

            if (res?.configData?.npsConfig) {
                const {npsConfig} = res?.configData || {};
                npsConfig && setNpsConfig(npsConfig);
            }

            if (res?.configData?.experienceConfig) {
                const {experienceConfig} = res?.configData || {};
                experienceConfig && setExperienceConfig(experienceConfig);
            }
        },
        [setExperienceConfig, setNpsConfig, setOngoingToast, setShowBotEntranceCard]
    );

    const getHistoryMsg = useCallback(
        async (
            arg: {
                sessionId: SessionId;
                currentMsgId?: MsgId;
                isFirstAutoPull?: boolean;
                needScrollToBottom?: boolean;
            },
            _ops?: {
                pagesStackSwitchScene?: boolean; // 是否页面栈切换场景；@wanghaoyu08
            }
        ) => {
            // const {pagesStackSwitchScene = false} = ops || {};

            const res = await getHistoryMsgFn({
                sessionId: arg.sessionId,
                ...(arg.currentMsgId ? {currentMsgId: arg.currentMsgId} : {})
            });
            if (isEmpty(res)) {
                return;
            }

            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            const {hasMore, msgData, msgIds} = res || {};

            // 自动拉取后，仍有历史消息；
            updateTriageSessionHasHistoryMsgAtom(arg.sessionId, hasMore || false);

            msgIds?.length &&
                msgData &&
                createMsgAtom(msgIds, msgData, {
                    type: 'unshift',
                    isFirstAutoPull: arg.isFirstAutoPull,
                    triggerSymbol: 'getHistoryMsg',
                    sourceApi: 'getHistory',
                    extData: {
                        hasHistoryMsg: hasMore
                    }
                });
        },
        [createMsgAtom]
    );
    /**
     * 获取核心数据
     *
     * @param type 类型，'init' 初始化，'create' 创建;
     */
    // eslint-disable-next-line complexity
    const getData = useCallback(
        (
            type: 'init' | 'create',
            ops?:
                | {
                      initType: 'default';
                  }
                | {
                      initType: 'switch';
                      switchTirgger?: NonNullable<
                          GetTriageStreamCoreParamsType['ctrlData']
                      >['pageFrom'];
                      switchSessionId?: SessionId;
                  }
        ): Promise<GetTriageStreamCoreRespType | null> => {
            // eslint-disable-next-line no-async-promise-executor, complexity
            return new Promise(async (resolve, reject) => {
                try {
                    const fkParams = await getFkParams();
                    const currentSessionId = curSessionId || urlSessionId || ''; // 本地缓存优先级高于 url 取值；
                    const showTab = !isSwanMsgListScene ? 1 : 0;

                    let ctrlData: GetTriageStreamCoreParamsType['ctrlData'] | undefined;
                    hasLoadedDataRef.current = false; // 重置 hasLoadedDataRef 标识； @wanghaoyu08

                    if (type === 'init') {
                        isCreateSessionSceneRef.current = false;

                        // 初始化优先从缓存里获取 sessionId，如果缓存里没有，则从 url 中获取 sessionId；如果都没有则不传递 sessionId；
                        let sessionIdToUse = currentSessionId;
                        let pageFrom;
                        let ctrlType;

                        if (refreshSymbol === 'pageRefresh') {
                            pageFrom = 'pageRefresh';
                        } else if (refreshSymbol === 'historyRecordPage') {
                            // 从历史对话记录页过来 不新开talk
                            pageFrom = 'sessionList';
                        }

                        if (urlCtrlType === 'uploadImgReport') {
                            ctrlType = 'uploadImgReport'; //医学报告解读
                        } else if (urlCtrlType === 'uploadImgSkin') {
                            ctrlType = 'uploadImgSkin'; // 皮肤病
                        }

                        if (ops && ops.initType === 'switch' && 'switchSessionId' in ops) {
                            sessionIdToUse = ops.switchSessionId || currentSessionId;
                        }

                        if (
                            ops &&
                            'initType' in ops &&
                            ops.initType === 'switch' &&
                            ops.switchTirgger
                        ) {
                            pageFrom = ops.switchTirgger;
                        }

                        ctrlData = {
                            currentSessionId: sessionIdToUse,
                            tmpBotVersion2: 1,
                            showTab,
                            ctrlType,
                            ...(pageFrom ? {pageFrom} : {})
                        };
                        ctrlData?.pageFrom && (pageFromRef.current = ctrlData?.pageFrom);
                    } else if (type === 'create') {
                        isCreateSessionSceneRef.current = true;

                        ctrlData = {
                            newSession: 1,
                            currentSessionId,
                            showTab
                        };
                    }

                    const r = await getCoreData({fkParams, ctrlData});

                    // Tips: 为控制时序，当前 switchSessionAction 事件在 getCoreData 之后执行同步操作；@wanghaoyu08
                    if (type === 'create') {
                        if (r?.session?.reUseSession) {
                            // 如果存在 reUseSession 标识，则复用当前会话数据无需重置刷新；
                            if (r?.session?.toast) {
                                hideLoading();
                                nextTick(() => {
                                    showToast({
                                        title: r.session.toast,
                                        icon: 'none',
                                        duration: 3000
                                    });
                                });
                            }
                            reject(new Error('reUseSession'));

                            return;
                        } else {
                            // 新建会话并清空数据；
                            curSessionId && switchSessionAction(curSessionId);
                        }
                    } else if (type === 'init') {
                        // 在处理消息数据之前先执行 switchSessionAction
                        if (ops?.initType === 'switch' && ops.switchSessionId) {
                            curSessionId && switchSessionAction(curSessionId, ops.switchSessionId);
                        } else if (ops?.initType === 'default') {
                            const sessionId = r?.session?.sessionId;
                            sessionId && switchSessionAction(r?.session?.sessionId, '', true);
                            msgIds && sessionId && resetTriageStreamMsgIdsAtom(msgIds, sessionId);
                        }
                    }
                    // 清除图片列表
                    eventCenter.trigger('clearImageList');
                    if (r?.session) {
                        const id = r?.session?.sessionId;
                        sessionId.current = id;

                        setCurSessionAtom(id);
                        getDataTriggeredRef.current = true;

                        updateDataForUbcAtom({
                            sessionId: id
                        });
                        getOtherData({
                            sessionId: id,
                            ctrlData: {
                                showTab
                            }
                        });

                        // 在 H5 环境下，将 sessionId 更新到 URL 参数中，用于跳转其他页面后返回重新加载数据的场景；@wanghaoyu08
                        if (process.env.TARO_ENV === 'h5') {
                            const url = new URL(window.location.href);
                            url.searchParams.set('sessionId', id);
                            url.searchParams.set('refreshSymbol', 'pageRefresh');
                            window.history.replaceState({}, '', url.toString());
                        }
                    }

                    if (r?.msgIds && r?.msgData) {
                        createMsgAtom(r?.msgIds, r?.msgData, {
                            type: 'push',
                            triggerSymbol: 'getCoreData',
                            sourceApi: 'getHistory', // 当前 core 接口不会返回数据，默认先置为 getHistory；@wanghaoyu08
                            extData: {
                                hasHistoryMsg: r?.session?.hasMorePreMsg,
                                isPullHistory: r?.session?.isPullHistory
                            }
                        });
                    }

                    if (r?.userData) {
                        setUserData(r?.userData);
                    }

                    if (r?.titleData) {
                        const {showHistoryEntry, showImCreateEntry} = r?.titleData || {};
                        const menu: ICMenuProps['menu'] = [];
                        showHistoryEntry &&
                            menu.push({
                                icon: 'history',
                                type: 'history',
                                isNeedLogin: true,
                                logValue: 'top_tips_order_guide_historyRecord_clk',
                                onLoginCallback: () => handleLoginBizAction(r?.session?.sessionId)
                            });
                        showImCreateEntry &&
                            menu.push({
                                icon: 'create',
                                type: 'createSession',
                                isNeedLogin: true,
                                logValue: 'ImCreateSession',
                                onLoginCallback: () => handleLoginBizAction(r?.session?.sessionId)
                            });
                        setTitleData({
                            ...r?.titleData,
                            menu
                        });
                    }

                    if (r?.inputData) {
                        setInputData(r?.inputData);
                    }

                    if (
                        r?.imActionData?.popData?.type === 'uploadImgReport' ||
                        r?.imActionData?.popData?.type === 'uploadImgSkin'
                    ) {
                        setPopData(r?.imActionData?.popData);
                    }

                    if (r?.bottomTips?.agreement) {
                        setAgreementInfo(r?.bottomTips?.agreement);
                    }

                    // Tips：风控场景数据临时处理，暂不耦合全局弹窗逻辑；@wanghaoyu08
                    if (
                        process.env.TARO_ENV === 'h5' &&
                        fkParams &&
                        r?.imActionData?.actionData?.actionType === 'antipass'
                    ) {
                        // eslint-disable-next-line no-console
                        console.warn('命中风控，需用户手动验证');

                        setAntiPassData({
                            data: r?.imActionData?.actionData,
                            refreshParams: {
                                sessionId: r?.session?.sessionId
                            },
                            fkParams
                        });

                        createHoldPromise();
                    } else {
                        releaseFirstConversation();
                    }

                    if (r?.statusData) {
                        const {orderGuideTip} = r?.statusData?.topTips || {};
                        const {titleInfo, viewOrderEntrys, viewOrderInfo} = orderGuideTip || {};

                        titleInfo && setTitleInfo(titleInfo);

                        // 为2025-06 冲刺项目上线，前端兼容后端晚上线的情况，后端上线后可删除
                        if (!isEmpty(viewOrderEntrys)) {
                            setViewOrderInfo(viewOrderEntrys);
                        } else {
                            setViewOrderInfo(viewOrderInfo);
                        }
                    }
                    if (r?.session?.hasMorePreMsg) {
                        if (r?.session?.isPullHistory) {
                            // 如果存在更多历史消息，且需要自动拉取，则需要拉取历史消息；
                            createHoldPromise();

                            await getHistoryMsg({
                                sessionId: r?.session?.sessionId,
                                isFirstAutoPull: true,
                                needScrollToBottom: !!ops?.initType
                            });
                            releaseFirstConversation();
                        } else {
                            updateTriageSessionHasHistoryMsgAtom(r?.session?.sessionId, true);
                        }
                    }
                    resolve(r as GetTriageStreamCoreRespType | null);

                    setCanRender(true);
                } catch (error) {
                    reject(error);
                    setCanRender(true);
                }
            });
        },
        [
            curSessionId,
            urlSessionId,
            refreshSymbol,
            urlCtrlType,
            msgIds,
            isSwanMsgListScene,
            setCurSessionAtom,
            getOtherData,
            createMsgAtom,
            setUserData,
            setTitleData,
            setInputData,
            setPopData,
            setAgreementInfo,
            setTitleInfo,
            setViewOrderInfo,
            getHistoryMsg,
            handleLoginBizAction
        ]
    );

    const getFirstConversation = useCallback(
        async (arg?: {isCreateSessionScene: boolean}) => {
            const isCreateSessionScene = arg?.isCreateSessionScene;

            if (holdPromiseRef.current) {
                await holdPromiseRef.current.promise;
            }

            await createConversation({
                msg: {
                    type: 'text' as const,
                    content: '',
                    sceneType: urlQuery || urlWord ? 'queryFirstCall' : 'defaultFirstCall'
                },
                withOutMsg: true,
                ctrlData: {
                    firstCall: true,
                    tmpBotVersion2: 1,
                    ...(isCreateSessionScene ? {newSession: 1} : {}),
                    ...(pageFromRef.current ? {pageFrom: pageFromRef.current} : {})
                },
                ...(urlIntent && !isCreateSessionScene ? {intent: urlIntent} : {})
            });
            pageFromRef.current = undefined;
            hasLoadedDataRef.current = true;
        },
        [createConversation, urlIntent, urlQuery, urlWord]
    );

    useEffect(() => {
        // Tips: 在 curSessionId 更新后，且 getData 触发更新后，执行 getFirstConversation；@wanghaoyu08
        // getFirstConversation 下游数据处理强依赖 curSessionIdAtom 必须为最新值，所以使用响应式而非链式调用；
        if (
            curSessionId &&
            curSessionId !== initiatedSessionIdRef.current &&
            getDataTriggeredRef.current
        ) {
            initiatedSessionIdRef.current = curSessionId;
            getDataTriggeredRef.current = false;
            getFirstConversation({
                isCreateSessionScene: isCreateSessionSceneRef.current
            });
            isCreateSessionSceneRef.current = false;
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [curSessionId]);

    // useDidShow(() => {
    //     // 针对手百消息列表入口，打开百度小程序页面栈变化，导致未拉取数据场景兼容；@wanghaoyu08
    //     if (!!isSwanMsgListScene && msgIds.length && hasLoadedDataRef.current && curSessionId) {
    //         let offsetMsgId = [...msgIds].pop();

    //         [...msgIds].reverse().some(id => {
    //             if (!isGenImMsgKey(id) && !id.startsWith(MSG_THINKING_MSG_KEY_PREFIX)) {
    //                 offsetMsgId = id;
    //                 return true;
    //             }
    //         });

    //         getHistoryMsg(
    //             {
    //                 sessionId: curSessionId,
    //                 currentMsgId: offsetMsgId
    //             },
    //             {
    //                 pagesStackSwitchScene: true
    //             }
    //         );
    //     }
    // });

    return {
        canRender,
        antiPassData,
        curSessionId,
        getData,
        getHistoryMsg,
        releaseFirstConversation
    };
};
