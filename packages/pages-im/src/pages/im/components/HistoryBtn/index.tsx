import cx from 'classnames';
import {useAtomValue} from 'jotai';
import {View, Image} from '@tarojs/components';
import {
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
    forwardRef,
    useImperativeHandle
} from 'react';
import {eventCenter, nextTick} from '@tarojs/taro';
import {vibrateShort} from '@baidu/vita-utils-shared';
import {useGetSessionId} from '../../../../hooks/triageStream/pageDataController';
import {
    useMsgDataGetController,
    useGetSessionMsgIds
} from '../../../../hooks/triageStream/dataController';
import {useInitDataController} from '../../../../hooks/triageStream/useInitDataController';
import {useScrollControl} from '../../../../hooks/common/useScrollControl';

import {triageSessionHasHistoryMsgAtom} from '../../../../store/triageStreamAtom/msg';
import {SCROLL_ANIMATION_DISABLE_ONCE} from '../../../../constants/common';
import {showToast} from '../../../../utils/customShowToast';

import {MsgId} from '../../../../typings';

import styles from './index.module.less';

const HistoryBtn = forwardRef((props: {msgId: MsgId | undefined}, ref) => {
    const {msgId} = props;
    const curSessionId = useGetSessionId();
    const {getHistoryMsg: getHistoryMsgFn} = useInitDataController();
    const oldFirstMsgId = useRef<MsgId | undefined>(msgId);
    const {scrollToElement, scrollToMessage} = useScrollControl();
    const {data} = useMsgDataGetController({msgId: msgId || ''});
    const hasHistoryMsg = useAtomValue(triageSessionHasHistoryMsgAtom(curSessionId || ''));
    const {msgIds} = useGetSessionMsgIds();

    const [loading, setLoading] = useState(false);

    const getHistoryMsg = useCallback(async () => {
        if (loading) return;
        try {
            vibrateShort();
            setLoading(true);
            await getHistoryMsgFn({
                sessionId: curSessionId || '',
                currentMsgId: data?.meta?.msgId
            });
            nextTick(() => {
                setLoading(false);
            });
        } catch (e) {
            showToast({
                title: '加载失败，请稍后再试',
                icon: 'none'
            });
            nextTick(() => {
                setLoading(false);
            });
        } finally {
            nextTick(() => {
                setLoading(false);
            });
        }
    }, [curSessionId, data?.meta?.msgId, getHistoryMsgFn, loading]);

    const renderLoadingDom = useMemo(() => {
        if (loading) {
            return (
                <Image
                    src='https://med-fe.cdn.bcebos.com/vita/tiaodong-loading-0815.gif'
                    className={styles.loading}
                />
            );
        } else if (msgIds?.length && msgIds?.length <= 10) {
            return (
                <View className='wz-flex'>
                    <View className={cx(styles.historyLine, styles.leftLine)} />
                    <View className='wz-plr-27'>下拉加载历史对话</View>
                    <View className={styles.historyLine} />
                </View>
            );
        }
    }, [loading, msgIds]);

    const genDom = useMemo(() => {
        if (hasHistoryMsg) {
            return (
                <View
                    className={cx(
                        styles.historyBtn,
                        'wz-fs-36 wz-fw-400 wz-mtb-42 wz-flex wz-justify-center'
                    )}
                    onClick={getHistoryMsg}
                >
                    {renderLoadingDom}
                </View>
            );
        }
    }, [hasHistoryMsg, getHistoryMsg, renderLoadingDom]);

    useImperativeHandle(ref, () => ({
        getHistoryMsg,
        hasHistoryMsg
    }));

    // 用于加载完历史消息后的位置恢复
    useEffect(() => {
        if (!msgId || !msgIds || msgIds?.length === 0) {
            return;
        }
        const tempOldFirstMsgId = oldFirstMsgId.current || msgId;
        // 当tempOldFirstMsgId不在数组第一个元素时，表示加载了新消息，滚动到前一个元素
        if (msgIds[0] !== tempOldFirstMsgId) {
            const currentIndex = msgIds.indexOf(tempOldFirstMsgId);
            if (currentIndex > 0) {
                const prevMsgId = msgIds[currentIndex - 1];
                eventCenter.trigger(SCROLL_ANIMATION_DISABLE_ONCE, true);
                if (process.env.TARO_ENV === 'h5') {
                    const element = document.getElementById(`msg-item-${prevMsgId}-scrollAnchor`);
                    if (element) {
                        element.scrollIntoView({block: 'start'});
                    }
                } else {
                    scrollToMessage(`${prevMsgId}-scrollAnchor`, 'historyBtn');
                }
            }
        }
        oldFirstMsgId.current = msgId;
    }, [msgId, msgIds, scrollToElement, scrollToMessage]);

    return genDom;
});
HistoryBtn.displayName = 'HistoryBtn';
export default memo(HistoryBtn);
