import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, useMemo, type FC, useCallback} from 'react';
import {ImText, ImCommon} from '@baidu/wz-taro-tools-core';
import {ImImage, ImFlow, ImSystem} from '@baidu/vita-ui-cards-common';

import {
    MSG_CARDID_TYPE,
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    MSG_CARDID_ENUM_STREAM_TYPE
} from '../../../../constants/msg';

import ImRichMsg from '../../../../components/ImRichMsg';
import ImThinking from '../../../../components/ImThinking';
import ImExpFocusDoctor from '../../../../components/ImExpFocusDoctor';
import ImBusinessDoctor from '../../../../components/ImBusinessDoctor';
import StreamFocusService from '../../../../components/StreamFocusServiceDocIm';

import {useGetSessionId} from '../../../../hooks/docIm/pageDataController';
import {useModalUpdate} from '../../../../hooks/modal/useGenGlobalModalWithAtom';
import {useGetCardEventCallback} from '../../../../hooks/useGetCardEventCallback';

import {ubcCommonViewSend, ubcCommonClkSend} from '../../../../utils/generalFunction/ubc';

import {docImAtomStore} from '../../../../store/docImAtom';
import {getBdFileMapAtom} from '../../../../store/docImAtom/bdFileMapAtom';

import {OwnerTypeEnum} from '../../../../typings/msg.type';
import type {ViewRenderModalStore} from '../../../../store/viewRenderAtom';

import {TABLE_COLORS} from '../../../../constants/common';
import {isPaidMed} from '../../../../utils';

import type {IProps} from './index.d';

import styles from './index.module.less';

const componentsMap = {
    // stream 新增消息卡片；
    [MSG_CARDID_ENUM_STREAM.ImFlow]: ImFlow,
    [MSG_CARDID_ENUM_STREAM.ImThinking]: ImThinking,

    // 原消息卡片；
    [MSG_CARDID_ENUM.ImText]: ImText,
    [MSG_CARDID_ENUM.ImImage]: ImImage,
    [MSG_CARDID_ENUM.ImCommon]: ImCommon,
    [MSG_CARDID_ENUM.ImRichMsg]: ImRichMsg,
    [MSG_CARDID_ENUM.ImSystemMsg]: ImSystem,
    [MSG_CARDID_ENUM.ImFocusDoctor]: ImExpFocusDoctor, // 新医生大卡
    [MSG_CARDID_ENUM.ImBusinessDoctor]: ImBusinessDoctor, // 商业化医生卡
    [MSG_CARDID_ENUM_STREAM.ImCollectedInfoAndSku]: StreamFocusService
};

const needBubbleWrapMsg: number[] = [MSG_CARDID_ENUM.ImText];

/**
 * 动态渲染卡片组件
 *
 * @param props 组件属性
 * @returns 渲染后的卡片组件
 */
const CardComponents: FC<IProps> = props => {
    const sessionId = useGetSessionId();
    const {data, msgId, isLatest, lastMsgId} = props;
    const {content} = data?.data || {};
    const {meta} = data || {};

    const {onOpenLink, onSendUbc} = useGetCardEventCallback();
    const {updateModalStore} = useModalUpdate(docImAtomStore);

    const dispatchEventCallback = useCallback(
        args => {
            const cbMap = {
                openLink: onOpenLink,
                modal: arg => {
                    const {info, cardData} = arg;
                    cardData &&
                        updateModalStore({
                            modalState: 1,
                            interaction: 'modal',
                            interactionInfo: info,
                            cardData,
                            msgData: data.data
                        } as unknown as ViewRenderModalStore);
                },
                sendUbc: arg => {
                    const mergeArg = {
                        ...arg,
                        info: {
                            ...arg.info,
                            params: {
                                ...(arg.info.params || {}),
                                ext: {
                                    ...(arg.info?.params?.ext || {}),
                                    product_info: {
                                        ...(arg.info?.params?.ext?.product_info || {})
                                    }
                                }
                            }
                        }
                    };
                    onSendUbc?.(mergeArg);
                }
            };
            cbMap[args?.interaction] &&
                cbMap[args?.interaction]?.({
                    info: args?.interactionInfo,
                    cardData: content
                });
        },
        [content, data.data, onOpenLink, onSendUbc, updateModalStore]
    );

    const extProp = useMemo(() => {
        return {
            [MSG_CARDID_ENUM_STREAM.ImAIRecommendUnDirect]: {
                localExt: data?.meta?.localExt
            },
            [MSG_CARDID_ENUM_STREAM.ImAIRecommendExpert]: {
                localExt: data?.meta?.localExt
            },
            [MSG_CARDID_ENUM.ImText]: {
                lineHeight: '87',
                color: '#000311'
            },
            [MSG_CARDID_ENUM.ImCommon]: {
                onThrowEvent: dispatchEventCallback
            },
            [MSG_CARDID_ENUM.ImFocusDoctor]: {
                onThrowEvent: dispatchEventCallback
            },
            [MSG_CARDID_ENUM.ImSystemMsg]: {
                onThrowEvent: arg => {
                    dispatchEventCallback(arg);

                    content?.ubc?.value &&
                        ubcCommonClkSend({
                            value: content.ubc.value,
                            ext: {
                                product_info: {
                                    msgId: msgId || '',
                                    ...content?.ubc?.production_info
                                }
                            }
                        });
                },
                onLoadEvent: () => {
                    content?.ubc?.value &&
                        ubcCommonViewSend({
                            value: content.ubc.value,
                            ext: {
                                product_info: {
                                    msgId: msgId || '',
                                    ...content?.ubc?.production_info
                                }
                            }
                        });
                }
            },
            [MSG_CARDID_ENUM.ImImage]: {
                data: {
                    ...(content?.data || {}),
                    cardStyle:
                        meta?.ownerType !== OwnerTypeEnum['系统']
                            ? {...(content?.data?.cardStyle || {}), width: 360, height: 360}
                            : content?.data?.cardStyle
                },
                mode: 'aspectFill',
                sendStatus: meta?.localMsgStatus,
                showloading: meta?.localMsgStatus === 'pending',
                isIm: meta?.ownerType !== OwnerTypeEnum['系统'],
                isPrivate: meta?.ownerType === OwnerTypeEnum['需求方'],
                bdFileMap: process.env.TARO_ENV === 'swan' ? getBdFileMapAtom() : undefined
            },
            [MSG_CARDID_ENUM.ImRichMsg]: {
                textExt: {
                    lineHeight: '87',
                    color: '#000311',
                    isPrivate: meta?.ownerType === OwnerTypeEnum['需求方']
                },
                imageExt: {
                    mode: 'aspectFill',
                    sendStatus: meta?.localMsgStatus,
                    showloading: meta?.localMsgStatus === 'pending',
                    isIm: meta?.ownerType !== OwnerTypeEnum['系统'],
                    isPrivate: meta?.ownerType === OwnerTypeEnum['需求方'],
                    bdFileMap: process.env.TARO_ENV === 'swan' ? getBdFileMapAtom() : undefined
                }
            },
            [MSG_CARDID_ENUM_STREAM.ImFlow]: {
                isLocalInterrupted: meta?.localMsgStatus === 'aborted',
                localExt: meta?.localExt,
                isLatest,
                lastMsgId,
                page: 'docIm',
                sessionId: sessionId || '',
                customStyles: isPaidMed()
                    ? {
                        rhetoricalQuestions: {
                            paidActiveBgColor: 'rgba(78, 110, 242, 0.15)'
                        },
                        quickReply: {
                            paidActiveBgColor: 'rgba(78, 110, 242, 0.15)'
                        },
                        markDown: {
                            table: {
                                // 标题颜色
                                thBgColors: TABLE_COLORS,
                                // 表格整体样式
                                tableStyle: {
                                    boxShadow: 'none'
                                },
                                // 表格内容样式
                                tableContentStyle: {
                                    overflow: 'hidden',
                                    borderRadius: 16,
                                    border: '0.33px solid #DBDBDB',
                                    boxSizing: 'border-box'
                                },
                                // 单元格样式
                                cellStyle: {
                                    fontSize: 16,
                                    boxSizing: 'border-box',
                                    padding: '8px 15px',
                                    minWidth: 114,
                                    color: '#000311',
                                    borderWidth: '0.33px',
                                    borderColor: '#DBDBDB'
                                },
                                // 首列样式
                                firstCellStyle: {
                                    minWidth: 102,
                                    backgroundColor: '#F5F6FA',
                                    color: '#848691'
                                },
                                // 表头样式
                                thCellStyle: {
                                    fontWeight: 500
                                }
                            }
                        }
                    }
                    : {}
            }
        };
    }, [
        isLatest,
        lastMsgId,
        dispatchEventCallback,
        data?.meta?.localExt,
        content?.data,
        content?.ubc?.value,
        content?.ubc?.production_info,
        meta?.ownerType,
        meta?.localMsgStatus,
        meta?.localExt,
        msgId,
        sessionId
    ]);

    const dynamicRender = useMemo(() => {
        const ComponentToRender =
            componentsMap[content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE | MSG_CARDID_TYPE];
        const extProps = extProp[content?.cardId as keyof typeof extProp] || {};

        if (!ComponentToRender) {
            return (
                <View className={cx('wz-ptb-36 wz-plr-39 wz-flex-col wz-col-bottom wz-fs-51')}>
                    当前版本暂不支持该消息渲染
                </View>
            );
        }

        return data?.type === 'dynamic' ? (
            <ComponentToRender data={data.data} msgId={msgId} {...extProps} />
        ) : (
            <ComponentToRender data={content.data} msgId={msgId} {...extProps} />
        );
    }, [content, data.data, data?.type, extProp, msgId]);

    return needBubbleWrapMsg.includes(
        content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE | MSG_CARDID_TYPE
    ) ? (
            <View
                className={cx(
                    meta?.ownerType === OwnerTypeEnum['需求方']
                        ? styles.bubbleWrapper
                        : styles.bubbleWrapperServicer,
                    isPaidMed() ? styles.bubbleWrapperPaidMed : '',
                    'wz-plr-45 wz-ptb-42 wz-flex-col wz-col-top wz-fs-51'
                )}
            >
                {dynamicRender}
            </View>
        ) : (
            dynamicRender
        );
};

export default memo(CardComponents);
