.imWelcomeModuleWrapper {
    width: 100%;
    padding-top: 81px;
    box-sizing: border-box;
}

.imWelcomeModule {
    position: relative;
    width: 100%;
    flex-shrink: 0;
    border: 3px solid #fff;
    background: rgb(255 255 255 / 30%);
    box-sizing: border-box;
    padding-top: 72px;

    .docDisplayImg {
        position: absolute;
        top: -81px;
        right: 45px;
        width: 360px;
        height: 480px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .docDisplayImgNew {
        width: 450px;
        height: 450px;
    }

    .imWelcomeModuleTitleConainer {
        width: 657px;

        .imWelcomeModuleTitle {
            display: inline-block;
            width: 100%;
            color: #000311;
            white-space: normal;
            overflow: hidden;
            word-break: break-all;
            font-family: PingFang SC;
            font-style: normal;
            font-weight: 500;
            margin-bottom: 28.5px;
        }

        .imWelcomeModuleText {
            width: 100%;
            color: #272933;
            line-height: 1.6 !important;
        }
    }

    .moduleSku {
        position: relative;
        width: 100%;
        border: 3px solid #fff;
        background: rgb(255 255 255 / 60%);
        backdrop-filter: blur(30px);
        box-sizing: border-box;

        .singleSkuContainer {
            width: 100%;
            padding: 60px 51px;
            box-sizing: border-box;

            .icon {
                width: 180px;
                height: 180px;
                border-radius: 50%;
                background-size: cover;
                background-repeat: no-repeat;
            }

            .skuCon {
                flex: 1;

                .skuTitle {
                    width: 100%;
                    color: #000311;
                    font-family: PingFang SC;
                    font-style: normal;
                    font-weight: 500;
                }

                .skuDesc {
                    width: 100%;
                    color: #848691;
                    min-height: 42px;
                    margin-top: 30px;
                }
            }

            .skuBtn {
                width: 246px;
                height: 111px;
                flex-shrink: 0;
                border-radius: 90px;
                background: linear-gradient(271deg, #00d3ea 0%, #00cfa3 100%);
                color: #fff;
                font-weight: 600;
                font-family: PingFang SC;
                line-height: 111px;
                text-align: center;
            }
        }

        .multiSkuContainer {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            padding: 45px;
            align-items: flex-start;
            justify-content: space-between;
            box-sizing: border-box;

            .skuItem {
                flex: 1;
                flex-shrink: 0;
                border-radius: 45px;
                background: #eff3f9;
                color: #000311;
                box-sizing: border-box;
                padding: 45px;
                align-items: center;
                justify-content: space-between;
                flex-direction: column;
                font-family: PingFang SC;
                font-weight: 500;
            }

            .skuItemOf2PerRow {
                flex-basis: calc(50% - 12px);
                max-width: calc(50% - 12px);
            }

            .skuItemOf3PerRow {
                flex-basis: calc(33.333% - 16px);
                max-width: calc(33.333% - 16px);
            }

            .icon {
                width: 180px;
                height: 180px;
                border-radius: 50%;
                background-size: cover;
                background-repeat: no-repeat;
            }
        }
    }
}

.replyText {
    color: #848691;
}
