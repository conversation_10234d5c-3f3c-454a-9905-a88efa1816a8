// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-24 17:07:54
// Description: 语音蒙层

import React, {
    memo,
    useCallback,
    useRef,
    useState,
    forwardRef,
    useImperativeHandle,
    useEffect
} from 'react';
import {Button} from '@baidu/wz-taro-tools-core';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';

// import type {AIVoiceToken} from '@src/components/im/ImVoice/VoiceInputModal/index.d';
import {useConversationDataController} from '../../../../hooks/triageStream/useConversationDataController';
import type {VoiceModalProps} from '../index.d';

// import { useTextareaSendFocus } from '../../hook/useTextareaSendFocus';
import styles from './index2.module.less';
import useVoice from './useVoice';

interface AIVoiceToken {
    expiresIn: number;
    tmpSecretId: string;
    tmpSecretKey: string;
    token: string;
    ttl: number;
}

const VoiceModal = memo(
    forwardRef((props: VoiceModalProps, ref) => {
        const {open = false, handleClose} = props;
        const [slideType, setSlideType] = useState<'init' | 'moveIn' | 'moveOut' | 'moveTop'>(
            'init'
        );
        // TODO: 暂时写死取消语音安全高度
        const cancelVoiceSafeHieght = 48;

        // 录音实例
        const aiVoiceToken = useRef<AIVoiceToken>(null);

        const {createConversation} = useConversationDataController();
        const {voiceStart, voiceStop, setVoiceResult, voiceResult} = useVoice({
            aiVoiceToken
        });

        // const { textareaRef } = useTextareaSendFocus();

        const resetData = useCallback(() => {
            setSlideType('init');
        }, []);

        const handleTouchStart = useCallback(() => {
            voiceStart();
            setSlideType('moveIn');
        }, [voiceStart]);

        // TODO:unknown暂时添加类型
        const handleTouchMove = useCallback((info: unknown) => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            const {deltaY} = info;
            console.info('deltaY', info);
            if (deltaY + cancelVoiceSafeHieght < 0) {
                setSlideType('moveTop');
            } else {
                setSlideType('moveIn');
            }
        }, []);

        /**
         * @description 结束录制语音，前置包含 【上滑取消】【发送】 两种状态
         */
        const handleTouchEnd = useCallback(() => {
            switch (slideType) {
                // 发送
                case 'moveIn':
                    if (voiceResult) {
                        createConversation({
                            msg: {
                                type: 'text',
                                content: voiceResult,
                                sceneType: 'inputAudio'
                            }
                        });
                    }
                    handleClose && handleClose();
                    break;
                // 上滑
                case 'moveTop':
                    handleClose && handleClose();
                    break;
                default:
                    break;
            }
            voiceStop();
            resetData();
        }, [createConversation, handleClose, resetData, slideType, voiceResult, voiceStop]);


        useEffect(() => {
            if (!open) {
                setVoiceResult('');
            }
        }, [open, setVoiceResult]);

        useImperativeHandle(ref, () => ({
            handleTouchStart,
            handleTouchMove,
            handleTouchEnd
        }));

        return (
           
            <>
                {
                    open?
                        <View className={styles.voiceModalMain}>
          
                            <View className={cx(styles.sendVoiceBtn, 'wz-flex', 'wz-plr-36')}>
                                <Text
                                    className={cx(
                                        styles.sendVoiceText,
                                        'wz-fs-42'
                                    )}
                                >
                                    {slideType === 'moveTop' ? '松开取消' : '松开发送  上滑取消'}
                                </Text>
                                <Button
                                    className={cx(
                                        styles.sendBtnItem,
                                        slideType === 'moveTop' ? styles.cancelBackgroundColor : ''
                                    )}
                                    id='voiceBtnItem'
                                    color='primary'
                                    shape='round'
                                >
                                    <View className={styles.voiceIngGif} />
                                </Button>
                            </View>
                        </View>: null
                }
            </>
        );
    })
);

VoiceModal.displayName = 'VoiceModal';

export default VoiceModal;
