import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>lerCallbackNode, IAsrError} from './index.d';

// H5 Web Speech API 语音转文字实现
// 参考: https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API

// Web Speech API 类型声明
interface SpeechRecognitionEvent extends Event {
    resultIndex: number;
    results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
    error: string;
    message: string;
}

interface SpeechRecognitionResult {
    isFinal: boolean;
    [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
    transcript: string;
    confidence: number;
}

interface SpeechRecognitionResultList {
    length: number;
    [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognition extends EventTarget {
    continuous: boolean;
    interimResults: boolean;
    lang: string;
    maxAlternatives: number;
    onstart: ((this: SpeechRecognition, ev: Event) => void) | null;
    onresult: ((this: SpeechR<PERSON>ognition, ev: SpeechRecognitionEvent) => void) | null;
    onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => void) | null;
    onend: ((this: SpeechRecognition, ev: Event) => void) | null;
    start(): void;
    stop(): void;
    abort(): void;
}

declare global {
    interface Window {
        SpeechRecognition: {
            new (): SpeechRecognition;
        };
        webkitSpeechRecognition: {
            new (): SpeechRecognition;
        };
    }
}

interface SpeechRecognitionConfig {
    mode: string;
    context: string;
    longSpeech: boolean;
}

interface IAsr extends IAsrHandler, IHandlerCallbackNode {}

class AsrHandler implements IAsr {
    private speechRecognition: SpeechRecognition | null = null;
    public canIUse = false;
    private configParams: SpeechRecognitionConfig;
    private isRecording = false;

    // 多端统一打平对外暴露的回调
    public onError: IHandlerCallbackNode['onError'];
    public onStart: IHandlerCallbackNode['onStart'];
    public onRecognize: IHandlerCallbackNode['onRecognize'];
    public onRecognizeStop: IHandlerCallbackNode['onRecognizeStop'];
    public onRecorderStop: IHandlerCallbackNode['onRecorderStop'];
    public onStop: IHandlerCallbackNode['onStop'];
    public onCancel: IHandlerCallbackNode['onCancel'];

    private _temText: string;
    private recognitionTimeout: number | null = null;

    constructor() {
        this._temText = '';
        this.initSpeechRecognition();
    }

    private initSpeechRecognition() {
        // 检查 Web Speech API 支持
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            // eslint-disable-next-line no-console
            console.warn('当前浏览器不支持 Web Speech API');
            this.canIUse = false;
            return;
        }

        this.speechRecognition = new SpeechRecognition();
        this.canIUse = true;

        // 配置语音识别
        this.speechRecognition.continuous = true;
        this.speechRecognition.interimResults = true;
        this.speechRecognition.lang = 'zh-CN'; // 中文语言，匹配 Swan 实现
        this.speechRecognition.maxAlternatives = 1;

        // 绑定事件处理器
        this.speechRecognition.onstart = () => {
            this.isRecording = true;
            this.onStart && this.onStart({});
        };

        this.speechRecognition.onresult = (event: SpeechRecognitionEvent) => {
            let interimTranscript = '';
            let finalTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            // 用最终结果更新累积文本
            if (finalTranscript) {
                this._temText += finalTranscript;
            }

            // 发送当前识别结果（累积 + 临时）
            const currentResult = this._temText + interimTranscript;
            this.onRecognize && this.onRecognize({result: currentResult});
        };

        this.speechRecognition.onerror = event => {
            if (event?.error) {
                const err = this.handleErr({errorCode: this.mapSpeechErrorToCode(event.error)});
                this.onError && this.onError(err as IAsrError);
            }
        };

        this.speechRecognition.onend = () => {
            this.isRecording = false;
            this.onStop && this.onStop({});
        };
    }

    private mapSpeechErrorToCode(error: string): number {
        // 将 Web Speech API 错误映射错误码
        switch (error) {
            case 'not-allowed':
            case 'permission-denied':
                return 2004; // 映射到麦克风权限错误
            case 'no-speech':
                return 4003; // 映射到无识别结果
            case 'audio-capture':
                return 3002; // 映射到设备冲突
            case 'network':
                return 2001; // 映射到网络/认证错误
            case 'service-not-allowed':
                return 2008; // 映射到服务认证错误
            case 'aborted':
                return 2005; // 映射到中止错误
            default:
                return 9999; // 未知错误
        }
    }

    private handleErr = res => {
        const errCode = +res?.errorCode;
        const __err: IAsrError = {errCode};

        switch (errCode) {
            case 3002:
            case 2001:
            case 2008:
                // 录音设备异常/冲突/鉴权失败
                __err.errType = 'deviceConflict';
                __err.errHandle = 'reStart';
                break;

            case 4003:
            case 3001:
                // 没有匹配的识别结果
                __err.errHandle = 'igorne';
                break;

            case 2004:
            case 2005:
                break;
            case 10003:
                __err.errToast = '请检查麦克风、录音权限';
                __err.errType = 'noMic';
                __err.errHandle = 'getMic';
                break;

            default:
                __err.errToast = `录音失败，请尝试使用文字输入${errCode}`;
                break;
        }

        return {...res, ...__err};
    };

    async start() {
        if (!this.canIUse || !this.speechRecognition) {
            return;
        }

        this._temText = '';
        this.configParams = {
            mode: 'touch',
            context: 'input',
            longSpeech: true
        };

        try {
            // 开始语音识别
            this.speechRecognition.start();

            // 设置最大录音时长超时（60秒）
            this.recognitionTimeout = window.setTimeout(() => {
                this.stop();
            }, 60000);
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('启动语音识别失败:', error);
            // const err = this.handleErr({errorCode: 2004});
            // this.onError && this.onError(err as IAsrError);
        }
    }

    stop() {
        if (this.recognitionTimeout) {
            window.clearTimeout(this.recognitionTimeout);
            this.recognitionTimeout = null;
        }

        // 停止语音识别
        if (this.speechRecognition && this.isRecording) {
            this.speechRecognition.stop();
        }
    }

    cancel() {
        if (this.recognitionTimeout) {
            window.clearTimeout(this.recognitionTimeout);
            this.recognitionTimeout = null;
        }

        // 中止语音识别
        if (this.speechRecognition && this.isRecording) {
            this.speechRecognition.abort();
        }

        this.onCancel && this.onCancel();
    }
}

export default AsrHandler;
