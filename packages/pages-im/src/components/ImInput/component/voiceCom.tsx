// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-14 17:53:19
// Description: 语音组件（包含长按按钮和语音蒙层）

import {View} from '@tarojs/components';
import React, {memo, useCallback, useRef, useState} from 'react';
import cx from 'classnames';
import {throttle} from 'lodash-es';
import {eventCenter, createSelectorQuery} from '@tarojs/taro';

import useTouchEvent from '../../../hooks/common/useTouchEvent';
import {useUpdateVoiceAsrIs} from '../../../store/triageStreamAtom/voiceAsr';

import VoiceModal from './voiceModal/index';
import styles from './index.module.less';

import type {VoiceComProps, VoiceModalRef} from './index.d';

const ImTriageVoice = memo((props: VoiceComProps) => {
    const {handleChangeIcon} = props;
    const {setVoiceAsrIs} = useUpdateVoiceAsrIs();

    const [open, setOpen] = useState(false);
    const popRef = useRef<VoiceModalRef>(null);

    // 获取VoiceModal的offset
    const getVoiceModalOffset = useCallback(() => {
        if (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'swan') {
            // 小程序环境使用createSelectorQuery
            const query = createSelectorQuery();
            query.select('.voice-modal-container')
                .boundingClientRect()
                .exec((res) => {
                    if (res[0]) {
                        const { top, left, width, height } = res[0];
                        console.log('VoiceModal offset:', { top, left, width, height });
                        return { top, left, width, height };
                    }
                });
        } else {
            // H5环境使用DOM API
            const element = document.querySelector('.voice-modal-container');
            if (element) {
                const rect = element.getBoundingClientRect();
                console.log('VoiceModal offset:', rect);
                return rect;
            }
        }
    }, []);

    const handleLongPress = useCallback(() => {
        setOpen(true);
        eventCenter.trigger('stopTTS');
        setVoiceAsrIs(true);
        getVoiceModalOffset();
    }, [setVoiceAsrIs, getVoiceModalOffset]);

    const handleClose = useCallback(() => {
        setOpen(false);
        setVoiceAsrIs(false);
    }, [setVoiceAsrIs]);

    const {info, onTouchFn} = useTouchEvent({
        onTouchEnd: (msg: unknown) => {
            popRef?.current?.handleTouchEnd?.(msg);
        },
        onTouchStart: (msg: unknown) => {
            popRef?.current?.handleTouchStart?.(msg);
        },
        isStopEvent: true,
        onTouchMove: throttle(() => {
            popRef?.current?.handleTouchMove?.(info);

        }, 100)
    });

    return (
        <>
            <View
                onLongTap={handleLongPress}
                onLongPress={handleLongPress}
                {...onTouchFn}
                className={cx(
                    styles.imTriageVoice,
                    'wz-flex'
                )}
            >
                按住开始说话
                <VoiceModal
                    ref={popRef}
                    open={open}
                    handleClose={handleClose}
                    handleChangeIcon={handleChangeIcon}
                    className="voice-modal-container"
                />
            </View>
        </>
    );
});

ImTriageVoice.displayName = 'ImTriageVoice';

export default ImTriageVoice;
