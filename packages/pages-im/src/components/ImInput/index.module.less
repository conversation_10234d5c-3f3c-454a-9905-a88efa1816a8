.ImInputSwanContainer {
    position: relative;
    background: inherit;

    /* background-color: #eff3f9; */

    .fadeTransition {
        z-index: 10;
        opacity: 0;
        max-height: 0; /* 初始隐藏状态 */
    }

    .fadeVisible {
        opacity: 1;
        max-height: 500px; /* 设置足够大的值，确保能容纳内容 */
        transition:
            opacity 300ms ease-in-out,
            max-height 300ms ease-in-out;
    }

    .featureCom {
        align-items: flex-end;

        .imInputMain {
            position: relative;
            min-height: 156px;
            background-color: #fff;
            border-radius: 45px;
            width: 100%;
            box-sizing: border-box;

            .textarea {
                margin-left: 24px;
                flex: 1;
                min-width: 0;
            }

            .textareaAutoHeight {
                height: 60px;
            }

            .textareaPlaceholder {
                font-size: 48px;
                color: #b7b9c1;
            }
        }

        .isSwanMsgInputMain {
            position: relative;
            min-height: 120px;
            background-color: #fff;
            border-radius: 66px;
            width: 100%;

            .textarea {
                margin-left: 24px;
                width: 100%;
                max-height: 192px;
                overflow: auto;
            }

            .textareaPlaceholder {
                font-size: 48px;
                color: #b7b9c1;
            }

            .textareaAutoHeight {
                height: 60px;
            }

            .swanMsgTextareaPlaceholder {
                font-size: 48px;
                color: #b7b9c1;
            }

            .bigFontSizeClass {
                font-size: 30px;
                color: #b7b9c1;
            }
        }

        .hasImgInputMain {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            border-top: 0.33px solid #e0e0e0;
        }

        .hasImgSwanMsgInputMain {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
            border-top: 0.33px solid #e0e0e0;
            padding: 20px 42px;
        }

        .sendBtn {
            width: 180px;
            height: 108px;
            font-size: 42px;
            line-height: 42px;
            padding: 0;
            background: linear-gradient(to left, #00d3ea, #00cfa3);
            margin-left: 30px;
            font-weight: 700;
        }

        .disableBtn {
            opacity: 0.5;
        }

        .isSwanMsgSendBtn {
            width: 220px;
            height: 100px;
            font-size: 42px;
            line-height: 42px;
            padding: 0;
            background: linear-gradient(to left, #00d3ea, #00cfa3);
            margin-left: 42px;
            margin-bottom: 10px;
            font-weight: 700;
        }
    }

    .stopGenerateImage {
        padding-left: 22.5px;
        padding-right: 5px;
        position: relative;
        right: 0;
        bottom: 0;

        .stopGenerateIcon {
            width: 84px;
            height: 84px;
        }
    }
}

.ImInputWebContainer {
    /* background-color: #eef3f9; */
    position: relative;

    .fadeTransition {
        z-index: 10;
        opacity: 0;
        max-height: 0; /* 初始隐藏状态 */
    }

    .fadeVisible {
        opacity: 1;
        max-height: 500px; /* 设置足够大的值，确保能容纳内容 */
        transition:
            opacity 300ms ease-in-out,
            max-height 300ms ease-in-out;
    }

    .featureCom {
        .imInputMain {
            min-height: 156px;
            background-color: #fff;
            border-radius: 45px;
            width: 100%;
            display: flex;
            align-items: center;

            .textarea {
                height: 100%;
                width: 686px;
                align-items: baseline;
                resize: none;
                flex: 1;
                min-width: 0;
            }
        }

        .hasImgBorder {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
            border-top: 0.33px solid #e0e0e0;
        }

        .sendBtn {
            width: 180px;
            height: 108px;
            font-size: 42px;
            line-height: 42px;
            padding: 0;
            background: linear-gradient(to left, #00d3ea, #00cfa3);
            margin-left: 30px;
            font-weight: 700;
        }

        .disableBtn {
            opacity: 0.5;
        }

        .placeholderContain {
            flex: 1;
            border: none;
            outline: none;
            background-color: transparent;
            font-size: 48px;
            line-height: 66px;

            ::placeholder {
                background-color: transparent;
                color: #848691;
            }
        }
    }

    .stopGenerateImage {
        padding-left: 22.5px;
        padding-right: 5px;
        position: relative;
        right: 0;
        bottom: 0;

        .stopGenerateIcon {
            width: 84px;
            height: 84px;
        }
    }
}
