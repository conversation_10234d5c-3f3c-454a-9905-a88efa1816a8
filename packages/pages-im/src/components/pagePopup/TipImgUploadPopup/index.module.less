.container {
    width: 100vw;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    max-height: calc(100vh - 320px);
    background-color: #fff;
}

.containerOfH5 {
    max-height: 85vh !important;
    background-color: #fff;
}

.tipsContainer {
    background-color: #fff;
    height: 100%;
    flex: 1;
    position: relative;
    scrollbar-width: none;
    scrollbar-color: transparent transparent;

    &::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
    }

    .mainTitle {
        position: relative;
        font-family: PingFangSC-Medium;
        color: #1f1f1f;
        letter-spacing: 0;
        width: 100%;
        background: #fff url("https://med-fe.cdn.bcebos.com/wenzhen-mini-app/nps_pop_bg.png")
            no-repeat top left;
        background-size: 120% 150%;

        .closeIcon {
            position: absolute;
            right: 54px;
            width: 48px;
            height: 49px;
        }
    }
}

.imgInfoMain {
    height: calc(100% - 60px);
    overflow-x: scroll;
    box-sizing: border-box;
    scrollbar-width: none;
    scrollbar-color: transparent transparent;

    &::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
    }

    .desc {
        font-family: PingFangSC-Regular;
        color: #1f1f1f;
        line-height: 75px;
        font-size: 45px;
    }

    .imgContainer {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
    }

    .imgItem {
        width: 342px;
        height: 342px;
        border-radius: 45px;
        background: #eff3f9;
        overflow: hidden;
        box-sizing: border-box;
    }

    .imgDesc {
        color: #848691;
    }

    .imgTips {
        width: 100%;
        border-radius: 45px;
    }

    .title {
        font-family: PingFangSC-Medium;
        margin: 60px 0 45px;
        color: #000311;
    }
}

.uploadSection {
    background: #fff;
    box-sizing: border-box;
    font-family: PingFangSC-Regular;
    font-size: 60px;
    color: #007aff;
    letter-spacing: 0;
    text-align: center;
    line-height: 60px;
    overflow: hidden;
    width: 100%;
    flex: 0 0 auto;
    justify-content: space-between;
    padding-bottom: 162px;
}

.operationSection {
    background: #fff;
    border-radius: 45px;
    font-size: 60px;
    color: #007aff;
    height: 180px;
}

.segment {
    background: #e0e0e0;
    width: 100%;
}

.footerBtn {
    width: 48%;
    height: 300px;
    font-size: 42px;
    font-weight: 700;
    color: #272933;
    background-color: #f5f6f9;
    border-radius: 45px;
    flex-direction: column;
    justify-content: center;

    .footBtnIcon {
        width: 90px;
        height: 90px;
    }
}

.whiteMask {
    background-image: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, #fff 100%);
    width: 100%;
    height: 360px;
    position: absolute;
    bottom: 0;
    left: 0;
}
