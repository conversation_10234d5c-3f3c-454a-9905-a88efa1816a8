/*
 * @Author: lijing106
 * @Description: 图片上传引导弹窗组件
 */
import cx from 'classnames';
import {View} from '@tarojs/components';
import {type FC, memo, useCallback, useEffect} from 'react';
import {pxTransform, previewImage, eventCenter, getCurrentPages} from '@tarojs/taro';
import {Popup, WImage} from '@baidu/wz-taro-tools-core';

import {useGetImageSource, useSetImageSource} from '../../../hooks/triageStream/pageDataController';

import Portal from '../../../../../ui-cards-common/Portal'; // monorepo 内部引入暂时使用相对路径，避免与 taro 分端构建冲突；@wanghaoyu08

import {API_HOST} from '../../../models/apis/host';
import {BUCKET_NAME, ONLINE_HOST} from '../../../constants/common';

import {previewPic} from '../../../utils/basicAbility/upload';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../../utils/generalFunction/ubc';

import type {TipImgUploadPopupProps} from './index.d';

import styles from './index.module.less';

const UPLOADTYPEMAP = {
    camera: '拍照',
    album: '上传图片'
};

const UPLOADTYPEICON = {
    camera: 'https://med-fe.cdn.bcebos.com/vita/vita_camera.png',
    album: 'https://med-fe.cdn.bcebos.com/vita/vita_upload.png'
};

/**
 *
 * @description 发图小贴士弹窗
 * @param props
 * @returns
 */

const bucketConfName =
    API_HOST && ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;
const TipImgUploadPopup: FC<TipImgUploadPopupProps> = (props: TipImgUploadPopupProps) => {
    const {
        open,
        tipsData,
        sceneType,
        uploadOps = ['camera', 'album'],
        picCount = 1,
        onSelectedPics,
        closeUploadPopup
    } = props;

    const {setImageSource} = useSetImageSource();
    const {imageSource} = useGetImageSource();
    // 预览图片
    const handleViewPic = useCallback(
        url => {
            const urls =
                tipsData?.content
                    ?.filter(item => item?.type === 'img')
                    ?.reduce((prev: string[], cur) => {
                        const imgs = cur?.data?.list?.map(imgItem => imgItem?.small) || [];
                        return [...prev, ...imgs];
                    }, []) || [];
            previewImage({
                current: url,
                urls: [...urls]
            });
        },
        [tipsData?.content]
    );

    const isCheckSameSource = useCallback(
        (sceneType: string) => {
            if (sceneType?.indexOf('_') > -1) {
                // eslint-disable-next-line no-unsafe-optional-chaining
                const [, scene] = sceneType?.split('_');

                return imageSource?.indexOf(scene) > -1;
            }

            if (imageSource?.indexOf('_') > -1) {
                // eslint-disable-next-line no-unsafe-optional-chaining
                const [, scene] = imageSource?.split('_');

                return sceneType?.indexOf(scene) > -1;
            }

            return imageSource === sceneType;
        },
        [imageSource]
    );

    // 渲染内容
    const renderContent = useCallback(() => {
        return (
            <>
                {tipsData?.content?.map(item => {
                    if (item?.type === 'title') {
                        return (
                            <View
                                className={cx(styles.title, 'wz-fs-54')}
                                key={item?.data?.value?.toString()}
                            >
                                {item?.data?.value}
                            </View>
                        );
                    }
                    if (item?.type === 'text') {
                        return (
                            <View
                                className={cx(styles.desc, 'wz-fs-48')}
                                key={item?.data?.value?.toString()}
                            >
                                <View className={cx(styles.desc, 'wz-fs-48')}>
                                    {item?.data?.value}
                                </View>
                            </View>
                        );
                    }
                    if (item?.type === 'img') {
                        return (
                            <View
                                className={cx(styles.imgContainer, 'wz-flex wz-mtb-45')}
                                key={item?.data?.list?.toString()}
                            >
                                {Array.isArray(item?.data?.list) &&
                                    item?.data?.list?.length > 0 &&
                                    item?.data?.list?.map((imgItem, index) => (
                                        <View key={`${imgItem?.small}_${index}`}>
                                            <View className={cx(styles.imgItem, 'wz-mr-30')}>
                                                <WImage
                                                    className={cx(styles.imgTips)}
                                                    src={imgItem?.small}
                                                    mode='aspectFill'
                                                    onClick={() => handleViewPic(imgItem?.small)}
                                                />
                                            </View>
                                            <View
                                                className={cx(
                                                    styles.imgDesc,
                                                    'wz-mt-30 wz-text-center wz-mr-30'
                                                )}
                                            >
                                                {imgItem?.desc || ''}
                                            </View>
                                        </View>
                                    ))}
                            </View>
                        );
                    }
                    return <></>;
                })}
            </>
        );
    }, [handleViewPic, tipsData]);

    const handleUpload = useCallback(
        async type => {
            try {
                if (!sceneType) {
                    console.error('[TipImgUploadPopup] sceneType is undefined');
                    return;
                }
                const pages = getCurrentPages();
                const curPage = pages[pages.length - 1];
                const path = curPage?.path?.indexOf('docIm') > -1 ? 'docIm' : 'im';
                ubcCommonClkSend({
                    value: `${path}_imgUpdatePopup_${type}`,
                    ext: {
                        product_info: {
                            scene: sceneType,
                            uploadType: uploadOps
                        }
                    }
                });

                closeUploadPopup();
                const preData = await previewPic({count: picCount, bucketConfName, btnType: type});
                if (!isCheckSameSource(sceneType)) {
                    eventCenter.trigger('clearImageList');
                }

                if (!preData || !Object.keys(preData)?.length) return;
                onSelectedPics && onSelectedPics(preData, {sceneType});
                setImageSource(sceneType);
                ubcCommonClkSend({
                    value: `${path}_imgUpdatePopup_${type}_completed`,
                    ext: {
                        product_info: {
                            scene: sceneType,
                            uploadType: uploadOps
                        }
                    }
                });
            } catch (error) {
                console.error(error);
            }
        },
        [
            sceneType,
            isCheckSameSource,
            closeUploadPopup,
            onSelectedPics,
            setImageSource,
            uploadOps,
            picCount
        ]
    );

    useEffect(() => {
        open &&
            ubcCommonViewSend({
                value: 'imgUpdatePopup',
                ext: {
                    product_info: {
                        scene: sceneType,
                        uploadType: uploadOps
                    }
                }
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    return (
        <Portal>
            <Popup
                open={open}
                rounded
                placement='bottom'
                onClose={closeUploadPopup}
                style={{
                    maxHeight: '100%',
                    background: 'transparent',
                    zIndex: 1050,
                    borderRadius: `${pxTransform(45)} ${pxTransform(45)} 0 0`
                }}
            >
                {/* <Popup.Close /> */}
                <View
                    className={cx(
                        styles.container,
                        process.env.TARO_ENV === 'h5' ? styles.containerOfH5 : ''
                        // 'wz-plr-36'
                    )}
                >
                    <View className={cx(styles.tipsContainer)}>
                        <View className={cx(styles.mainTitle, 'wz-fs-54 wz-text-center wz-ptb-54')}>
                            {tipsData?.title || '发图小贴士'}
                            <WImage
                                onClick={closeUploadPopup}
                                className={styles.closeIcon}
                                src='https://med-fe.cdn.bcebos.com/vita/vita_nofillclose.png'
                            />
                        </View>
                        <View className={cx(styles.imgInfoMain, 'wz-pt-12', 'wz-plr-45')}>
                            {renderContent()}
                        </View>
                        {/* <View className={styles.whiteMask} /> */}
                    </View>

                    <View className={cx(styles.uploadSection, 'wz-flex', 'wz-plr-45', 'wz-pt-63')}>
                        {uploadOps &&
                            uploadOps?.length > 0 &&
                            uploadOps.map((uploadType, index) => {
                                return (
                                    <View
                                        key={index}
                                        className={cx(styles.footerBtn, 'wz-flex')}
                                        onClick={() => {
                                            handleUpload(uploadType);
                                        }}
                                    >
                                        <WImage
                                            className={cx(styles.footBtnIcon, 'wz-pb-30')}
                                            src={UPLOADTYPEICON[uploadType]}
                                        />
                                        {UPLOADTYPEMAP[uploadType]}
                                    </View>
                                );
                            })}
                    </View>
                    {/* <View
                        className={cx(
                            styles.operationSection,
                            'wz-flex wz-col-center wz-row-center wz-mt-30'
                        )}
                        onClick={closeUploadPopup}
                    >
                        取消
                    </View> */}
                </View>
            </Popup>
        </Portal>
    );
};

TipImgUploadPopup.displayName = 'TipImgUploadPopup';

export default memo(TipImgUploadPopup);
