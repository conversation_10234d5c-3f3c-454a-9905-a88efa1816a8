// Author: liulei57
// Date: 2025-08-27 15:03:35
// Description: 语音实时输入状态

import {atom, useAtomValue, useSetAtom} from 'jotai';

//是否开启语音实时输入
export const voiceAsrIs = atom<boolean>(false);
voiceAsrIs.debugLabel = 'voiceAsrIs';

/**
 *
 * @returns 获取语音实时输入状态
 */
export const useGetVoiceAsrIs = () => useAtomValue(voiceAsrIs);

/**
 *
 * @returns 更新语音实时输入状态
 */
export const useUpdateVoiceAsrIs = () => {
    const setVoiceAsrIs = useSetAtom(voiceAsrIs);
    return {
        setVoiceAsrIs
    };
};
