import {atom} from 'jotai';

import type {AgreementDataType, ExperienceConfig} from '../../models/services/triageStream/index.d';

import type {
    TitleInfoType,
    OngoingToastType,
    ViewOrderEntrysType,
    ViewOrderInfoType,
    NpsConfig
} from './index.type';

import {triageStreamAtomStore} from './index';

export const ongoingToastAtom = atom<OngoingToastType>();
ongoingToastAtom.debugLabel = 'ongoingToastAtom';

export const showBotEntranceCardAtom = atom<string | undefined>();
showBotEntranceCardAtom.debugLabel = 'showBotEntranceCardAtom';

export const titleInfoAtom = atom<TitleInfoType>();
titleInfoAtom.debugLabel = 'titleInfoAtom';

// TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除ViewOrderInfoType
export const viewOrderInfoAtom = atom<ViewOrderEntrysType | ViewOrderInfoType>();
viewOrderInfoAtom.debugLabel = 'viewOrderInfoAtom';

export const agreementInfoAtom = atom<AgreementDataType | undefined>();
agreementInfoAtom.debugLabel = 'agreementInfoAtom';

// nps相关配置
export const npsConfigAtom = atom<NpsConfig>();
npsConfigAtom.debugLabel = 'npsConfigAtom';

// 体验相关配置
export const experienceConfigAtom = atom<ExperienceConfig>();
experienceConfigAtom.debugLabel = 'experienceConfigAtom';

export const getAgreementInfoAtom = () => {
    return triageStreamAtomStore.get(agreementInfoAtom);
};

export const resetAgreementInfoAtom = () => {
    return triageStreamAtomStore.set(agreementInfoAtom, undefined);
};

// 获取体验实验配置
export const getExperienceConfig = () => {
    return triageStreamAtomStore.get(experienceConfigAtom);
};
