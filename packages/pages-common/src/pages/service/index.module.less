.serviceContainer {
    height: 100%;

    .innerContent {
        display: flex;
        flex-direction: column;
        height: 100%;

        .scrollContent {
            flex: 1;
            min-height: 0;

            ::-webkit-scrollbar {
                display: none;
            }

            .itemContainer {
                padding-bottom: 90px;
            }
        }
    }

    .tabsTitle {
        padding-bottom: 45px;

        /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
        :global {
            .h-tabs-list-scroll_wrap {
                height: 126px;
            }
        }
    }

    .service {
        width: 100%;
        padding: 0 51px;
        background: #ccc;
    }

    .serviceItem {
        padding: 0 36px;
        margin-bottom: 30px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .secondaryTab {
        padding-bottom: 36px;

        /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
        :global {
            .h-tabs-list-scroll {
                padding-left: 36px;
            }

            .h-tabs-list-scroll_wrap {
                height: 111px;
            }
        }
    }

    .loadingStatus {
        display: flex;
        justify-content: center;
        padding: 30px 0;
        font-size: 36px;
        color: #969799;
    }

    .noMore {
        text-align: center;
        font-size: 36px;
        color: #969799;
        padding: 30px 0;
    }

    .item {
        height: 300px;
        background: #c8c8c8;
        margin-top: 30px;
    }

    .skeleton {
        padding: 0 36px;

        .skeletonBlock {
            padding: 60px 45px;
            margin-bottom: 30px;
            border-radius: 63px;
            background: #fff;
            align-items: flex-start;

            .skeletonLeft {
                margin-right: 30px;
                width: 150px;
                height: 150px;
                border-radius: 50%;
                background: #eff3f9;
            }

            .skeletonRight {
                flex: 1;

                .searchLine {
                    width: 100%;
                    height: 42px;
                    margin-bottom: 18px;
                    border-radius: 12px;
                    background: #eff3f9;

                    &.searchLine1 {
                        width: 285px;
                        margin-bottom: 30px;
                    }

                    &.searchLine2 {
                        width: 660px;
                        margin-bottom: 0;
                    }
                }
            }
        }
    }

    .skeletonContainer {
        .skeletonTab {
            padding: 54px 36px 75px;

            .skeletonTabItem {
                width: 216px;
                height: 54px;
                background: #fff;
                margin-right: 78px;
                border-radius: 12px;
            }
        }
    }

    .empty {
        font-family: PingFang SC;
        width: 100%;

        --empty-description-padding: 14px;

        .emptyImg {
            width: 330px;
            height: 330px;
        }
    }
}
