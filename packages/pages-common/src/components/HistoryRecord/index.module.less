@import url('../../../../pages-im/src/style/variable.less');

.historyCon {
    font-family: PingFangSC-Medium;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
    z-index: @zIndex-modal;

    .historyTitle {
        width: 100%;
        height: 162px;
        color: #1f1f1f;
        box-sizing: border-box;
        border-bottom: 1px solid #e0e0e0;
        flex-shrink: 0;
    }

    .historyListWrap {
        flex: 1;
        min-height: 0;
        box-sizing: border-box;
        padding: 60px 36px 0;

        ::-webkit-scrollbar {
            display: none;
        }

        .historyItemWrap {
            margin-bottom: 30px;
            padding: 60px 60px 60px 45px;
            border-radius: 45px;
            background: #fff;

            &.btnActive {
                padding-right: 30px;
                background-image: linear-gradient(270deg, rgb(255 255 255 / 0%) 50%, #e5f9f9 100%);
            }
        }

        .historyItem {
            margin-right: auto;
            overflow: hidden;
            flex: 1;

            .historyText {
                line-height: 1.2;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .historyTime {
                color: #858585;
                font-family: PingFang SC;
            }
        }

        .historyRecordEmpty {
            font-family: PingFang SC;
            width: 100%;
            padding-top: 252px;

            --empty-description-padding: 14px;

            .emptyImg {
                width: 330px;
                height: 330px;
            }
        }
    }

    .operate {
        flex-shrink: 0;

        .cancelBtn {
            border-radius: 27px;
            height: 114px;
            padding: 33px 45px;
            font-size: 48px;
            line-height: 48px;
            box-sizing: border-box;
            font-family: PingFangSC-Regular;
            color: #848691;
            border: none;
        }

        .delBtn {
            height: 114px;
            border-radius: 27px;
            font-size: 48px;
            line-height: 48px;
            padding: 33px 30px;
            background: #ff5f49;
            color: #fff;
            border: none;

            &Icon {
                padding-left: 24px;
            }
        }
    }

    .safeArea {
        background: transparent !important;
    }
}

.skeletonItem {
    padding: 60px 60px 60px 45px;
    background: #fff;
    border-radius: 45px;
    margin-bottom: 30px;
}

.skeletonText {
    height: 48px;
    background: #eff3f9;
    border-radius: 12px;
    margin-bottom: 30px;
}

.skeletonTime {
    height: 39px;
    width: 200px;
    background: #eff3f9;
    border-radius: 12px;
}

.skeletonDelete {
    width: 60px;
    height: 60px;
    background: #eff3f9;
    border-radius: 12px;
}

.loadingText {
    font-family: PingFang SC;
    color: #858585;
}
