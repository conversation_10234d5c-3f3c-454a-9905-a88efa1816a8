.applicationStructure {
    display: flex;
    padding: 60px 45px 51px;
    background: #fff;
    border-radius: 63px;

    .applicationImg {
        margin-right: 30px;

        .inner {
            position: relative;
            width: 150px;
            height: 150px;

            .avatar {
                width: 150px;
                height: 150px;

                &.circle {
                    border-radius: 50%;
                    border: 3px solid rgb(220 221 224 / 30%);
                }
            }

            .cornerIcon {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 60px;
                height: 60px;
                border-radius: 50%;
            }
        }
    }

    .applicationDetail {
        flex: 1;
        min-width: 0;

        .personal {
            display: flex;
            align-items: center;
            color: #000311;
            font-size: 42px;
            line-height: 42px;
            margin-bottom: 21px;

            .name {
                font-weight: 500;
                font-family: PingFang SC;
                margin-right: 18px;
                flex-shrink: 0;
            }

            .professional {
                margin-right: 18px;
                flex-shrink: 0;
            }

            .department {
                flex-shrink: 1;
            }
        }

        .external {
            display: inline-flex;
            align-items: center;
            color: #000311;
            font-size: 42px;
            font-weight: 700;
            line-height: 42px;
            padding: 9px 0 21px;
            width: 100%;

            .hospital {
                margin-right: 18px;
                font-weight: 500;
                font-family: PingFang SC;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .rankLabel {
                display: flex;
                align-items: center;
                padding: 6px 12px;
                background: #e6f6ed;
                border-radius: 12px;
                flex-shrink: 0;

                .rank {
                    font-weight: 400;
                    color: #39b362;
                }
            }
        }

        .description {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            color: #848691;
            font-size: 42px;
            line-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
